﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Scriban.5.7.0\build\Scriban.props" Condition="Exists('..\packages\Scriban.5.7.0\build\Scriban.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D333B778-4E05-4E09-BE88-35010FCFFFC7}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RediSoftware</RootNamespace>
    <AssemblyName>Thermarate-Assessment</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <Use64BitIISExpress>
    </Use64BitIISExpress>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>8</LangVersion>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ExcludeGeneratedDebugSymbol>false</ExcludeGeneratedDebugSymbol>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ABCpdf, Version=10.1.1.5, Culture=neutral, PublicKeyToken=a7a0b3f5184f2169, processorArchitecture=MSIL">
      <HintPath>..\packages\ABCpdf.10.1.1.5\lib\net40\ABCpdf.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="AirMovementCalculator, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\AirMovementCalculator.dll</HintPath>
    </Reference>
    <Reference Include="Autofac, Version=3.5.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.3.5.2\lib\net40\Autofac.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Autofac.Integration.WebApi, Version=3.4.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.WebApi2.3.4.0\lib\net45\Autofac.Integration.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="AutoMapper, Version=5.1.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.5.1.1\lib\net45\AutoMapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="AWSSDK">
      <HintPath>..\packages\AWSSDK.2.3.51.0\lib\net45\AWSSDK.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.6.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.8.6\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper, Version=2.0.0.0, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>..\packages\CsvHelper.2.16.3.0\lib\net45\CsvHelper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.5\lib\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Elmah">
      <HintPath>..\packages\elmah.corelibrary.1.2.2\lib\Elmah.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=7.2.1.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.7.2.1\lib\net35\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.Interfaces, Version=6.1.1.0, Culture=neutral, PublicKeyToken=a694d7f3b0907a61, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.Interfaces.6.1.1\lib\net35\EPPlus.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.System.Drawing, Version=6.1.1.0, Culture=neutral, PublicKeyToken=2308d35469c9bac0, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.System.Drawing.6.1.1\lib\net35\EPPlus.System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="ExcelMapper, Version=*******, Culture=neutral, PublicKeyToken=6c3f2bec99465df3, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelMapper.5.2.328\lib\net461\ExcelMapper.dll</HintPath>
    </Reference>
    <Reference Include="FreeSpire.XLS, Version=14.2.0.0, Culture=neutral, PublicKeyToken=f07069cb8ea6e300, processorArchitecture=MSIL">
      <HintPath>..\packages\FreeSpire.XLS.14.2.0\lib\net48\FreeSpire.XLS.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.Core.1.7.5\lib\net45\GeoAPI.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI.CoordinateSystems, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.CoordinateSystems.1.7.5\lib\net45\GeoAPI.CoordinateSystems.dll</HintPath>
    </Reference>
    <Reference Include="GlazingCalculatorV1, Version=*******, Culture=neutral, PublicKeyToken=null">
      <HintPath>lib\GlazingCalculatorV1.dll</HintPath>
    </Reference>
    <Reference Include="GlazingCalculatorV2, Version=2.0.0.0, Culture=neutral, PublicKeyToken=null">
      <HintPath>lib\GlazingCalculatorV2.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis, Version=1.68.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.68.0\lib\netstandard2.0\Google.Apis.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=1.68.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.68.0\lib\netstandard2.0\Google.Apis.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.68.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.68.0\lib\netstandard2.0\Google.Apis.Core.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.FirebaseDynamicLinks.v1, Version=1.38.0.1548, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.FirebaseDynamicLinks.v1.1.38.0.1548\lib\net45\Google.Apis.FirebaseDynamicLinks.v1.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Oauth2.v2, Version=1.68.0.1869, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Oauth2.v2.1.68.0.1869\lib\netstandard2.0\Google.Apis.Oauth2.v2.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Sheets.v4, Version=1.27.1.883, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Sheets.v4.1.27.1.883\lib\net45\Google.Apis.Sheets.v4.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Urlshortener.v1, Version=**********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Urlshortener.v1.**********\lib\net45\Google.Apis.Urlshortener.v1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="HiQPdf, Version=*******, Culture=neutral, PublicKeyToken=acd8b62594985b24, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\HiQpdf\HiQPdf.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=*******, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.3.1\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="InteractivePreGeneratedViews, Version=*******, Culture=neutral, PublicKeyToken=46c4868af4307d2c, processorArchitecture=MSIL">
      <HintPath>..\packages\EFInteractiveViews.1.0.1\lib\net45\InteractivePreGeneratedViews.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="jint, Version=********, Culture=neutral, PublicKeyToken=2e92ba9c8d81157f, processorArchitecture=MSIL">
      <HintPath>..\packages\jint.2.10.3\lib\net45\jint.dll</HintPath>
    </Reference>
    <Reference Include="Json2KeyValue, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Json2KeyValue.*******\lib\net40\Json2KeyValue.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.3\lib\net40-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.4\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.4\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.4\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.WebUtilities, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.WebUtilities.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.5.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.Sqlite, Version=5.0.10.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Sqlite.Core.5.0.10\lib\netstandard2.0\Microsoft.Data.Sqlite.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=3.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.3.1.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=3.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.3.1.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.EnvironmentVariables, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.EnvironmentVariables.2.2.4\lib\netstandard2.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.FileExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.FileExtensions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.FileExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.2.2.0\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Physical, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Physical.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Physical.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileSystemGlobbing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileSystemGlobbing.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.ObjectPool.2.2.0\lib\netstandard2.0\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=3.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.3.1.0\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client, Version=4.60.3.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.4.60.3\lib\netstandard2.0\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=6.35.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.6.35.0\lib\net461\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Net.Http.Headers, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.Headers.2.2.0\lib\netstandard2.0\Microsoft.Net.Http.Headers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.2.2\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.4.2.2\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.2.2\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.2.2\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Google, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Google.4.2.2\lib\net45\Microsoft.Owin.Security.Google.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.MicrosoftAccount, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.MicrosoftAccount.4.2.2\lib\net45\Microsoft.Owin.Security.MicrosoftAccount.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.0.1\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.1016.290\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="NetTopologySuite, Version=2.0.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.2.1.0\lib\netstandard2.0\NetTopologySuite.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.Features, Version=2.0.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.Features.2.1.0\lib\netstandard2.0\NetTopologySuite.Features.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO, Version=1.14.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.IO.1.14.0.1\lib\net45\NetTopologySuite.IO.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO.GeoTools, Version=1.14.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.IO.1.14.0.1\lib\net45\NetTopologySuite.IO.GeoTools.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO.MsSqlSpatial, Version=1.14.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.IO.1.14.0.1\lib\net45\NetTopologySuite.IO.MsSqlSpatial.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO.PostGis, Version=1.14.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.IO.1.14.0.1\lib\net45\NetTopologySuite.IO.PostGis.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO.ShapeFile, Version=1.14.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.IO.1.14.0.1\lib\net45\NetTopologySuite.IO.ShapeFile.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO.ShapeFile.Extended, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.IO.1.14.0.1\lib\net45\NetTopologySuite.IO.ShapeFile.Extended.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO.SpatiaLite, Version=2.0.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1">
      <HintPath>..\packages\NetTopologySuite.IO.SpatiaLite.2.0.0\lib\netstandard2.0\NetTopologySuite.IO.SpatiaLite.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.5.4.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.4\lib\net45\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.5.4.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.4\lib\net45\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.5.4.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.4\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.5.4.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.4\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="NVelocity">
      <HintPath>..\packages\NVelocity.1.0.3\lib\NVelocity.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Postmark, Version=4.7.5.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Postmark.4.7.5\lib\netstandard2.0\Postmark.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="PuppeteerSharp, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\PuppeteerSharp.2.0.4\lib\netstandard2.0\PuppeteerSharp.dll</HintPath>
    </Reference>
    <Reference Include="QRCoder, Version=1.2.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.2.2\lib\net40\QRCoder.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ScratchOutputTools">
      <HintPath>lib\ScratchOutputTools.dll</HintPath>
    </Reference>
    <Reference Include="ScratchTools, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\ScratchTools.dll</HintPath>
    </Reference>
    <Reference Include="Scriban, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Scriban.5.7.0\lib\netstandard2.0\Scriban.dll</HintPath>
    </Reference>
    <Reference Include="SkiaSharp, Version=1.68.0.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\packages\SkiaSharp.1.68.0\lib\net45\SkiaSharp.dll</HintPath>
    </Reference>
    <Reference Include="Spire.XLS, Version=14.2.0.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\FreeSpire.XLS.14.2.0\lib\netstandard2.0\Spire.XLS.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.batteries_v2, Version=2.0.6.1341, Culture=neutral, PublicKeyToken=8226ea5df37bcae9, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.bundle_e_sqlite3.2.0.6\lib\net461\SQLitePCLRaw.batteries_v2.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.core, Version=2.0.6.1341, Culture=neutral, PublicKeyToken=1488e028ca7ab535, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.core.2.0.6\lib\netstandard2.0\SQLitePCLRaw.core.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.provider.dynamic_cdecl, Version=2.0.6.1341, Culture=neutral, PublicKeyToken=b68184102cba0b3b, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.provider.dynamic_cdecl.2.0.6\lib\netstandard2.0\SQLitePCLRaw.provider.dynamic_cdecl.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.provider.e_sqlite3, Version=2.0.6.1341, Culture=neutral, PublicKeyToken=9c301db686d0bd12, processorArchitecture=MSIL">
      <HintPath>..\packages\SQLitePCLRaw.provider.e_sqlite3.2.0.6\lib\netstandard2.0\SQLitePCLRaw.provider.e_sqlite3.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.CodeDom, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.CodeDom.7.0.0\lib\netstandard2.0\System.CodeDom.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=1.2.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.1.5.0\lib\netstandard2.0\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.5.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Device" />
    <Reference Include="System.Diagnostics.Contracts, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=6.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.6.0.1\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Management, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Management.7.0.2\lib\netstandard2.0\System.Management.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Metadata, Version=1.4.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.Metadata.1.6.0\lib\netstandard2.0\System.Reflection.Metadata.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.5.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Pkcs, Version=4.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Pkcs.4.5.0\lib\net461\System.Security.Cryptography.Pkcs.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Xml, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Xml.4.5.0\lib\net461\System.Security.Cryptography.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.5.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.5.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.4.5.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.5.0.1\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.5.0.2\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Dataflow, Version=4.5.24.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Tpl.Dataflow.4.5.24\lib\portable-net45+win8+wpa81\System.Threading.Tasks.Dataflow.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.0.1\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.0.2\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.0.0\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.0.1\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.0.1\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.0.1\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="TwoFactorAuth.Net, Version=1.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\TwoFactorAuth.Net.1.4.0\lib\net46\TwoFactorAuth.Net.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.2.2\lib\net40\UnityEngine.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebDriver, Version=3.141.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Selenium.WebDriver.3.141.0\lib\net45\WebDriver.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="Zlib.Portable, Version=1.11.0.0, Culture=neutral, PublicKeyToken=431cba815f6a8b5b, processorArchitecture=MSIL">
      <HintPath>..\packages\Zlib.Portable.Signed.1.11.0\lib\portable-net4+sl5+wp8+win8+wpa81+MonoTouch+MonoAndroid\Zlib.Portable.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include=".gitignore" />
    <Content Include="3DGlue10-32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="3DGlue10-64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="ABCpdf10-32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="ABCpdf10-64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="abcpdflicense.txt" />
    <Content Include="apple-touch-icon.png" />
    <Content Include="app\classes\BulkEdit.js" />
    <Content Include="app\classes\SortInfo.js" />
    <Content Include="app\classes\TestDummyData.js" />
    <Content Include="app\classes\StandardModelConstants.js" />
    <Content Include="app\classes\WholeOfHomeConstants.js" />
    <Content Include="app\components\directives\auto-grow-input.js" />
    <Content Include="app\components\directives\formatted-number [BACKUP].js" />
    <Content Include="app\components\directives\capitalize-directive.js" />
    <Content Include="app\components\directives\form-error-check.js" />
    <Content Include="app\components\directives\image-on-load-directive.js" />
    <Content Include="app\components\directives\jSignature-directive.js" />
    <Content Include="app\components\directives\max-lines-directive.js" />
    <Content Include="app\components\directives\formatted-number.js" />
    <Content Include="app\components\directives\number-to-string-directive.js" />
    <Content Include="app\components\directives\pattern-model-overwrite-directive.js" />
    <Content Include="app\components\directives\query-builder-directive.js" />
    <Content Include="app\components\directives\image-loader-spinner-directive.js" />
    <Content Include="app\components\directives\redi-show-by-role.js" />
    <Content Include="app\components\directives\string-to-number-directive.js" />
    <Content Include="app\components\directives\templates\custom-pagination.html" />
    <Content Include="app\components\directives\templates\decorated-created-by-template.tpl.html" />
    <Content Include="app\components\directives\templates\decorated-contact-name-template.tpl.html" />
    <Content Include="app\components\directives\templates\decorated-first-name-template.tpl.html" />
    <Content Include="app\components\directives\templates\decorated-full-name-template.tpl.html" />
    <Content Include="app\components\directives\templates\decorated-drop.tpl.html" />
    <Content Include="app\components\directives\templates\decorated-template.tpl.html" />
    <Content Include="app\components\directives\templates\jSignature-canvas-tpl.html" />
    <Content Include="app\components\directives\templates\modalDialog.tpl.html" />
    <Content Include="app\components\directives\templates\query-builder-tpl.html" />
    <Content Include="app\components\directives\transclude-device-data.js" />
    <Content Include="app\components\fields\select-all-multi-select.js" />
    <Content Include="app\components\fields\select-all-multi-select.html" />
    <Content Include="app\components\filters\format-boolean.js" />
    <Content Include="app\components\filters\in-array.js" />
    <Content Include="app\components\filters\trust-as-resource-url.js" />
    <Content Include="app\components\security\account\twofacauthform-controller.js" />
    <Content Include="app\components\security\account\twofacauthform.html" />
    <Content Include="app\components\security\authorization\authorization-enableroles-directive.js" />
    <Content Include="app\components\services\common\buildqueryparameters-service.js" />
    <Content Include="app\components\services\common\daterangehelper-service.js" />
    <Content Include="app\components\services\coreloop.js" />
    <Content Include="app\img\icons\more_vert.svg" />
    <Content Include="app\img\icons\north-compass-direction.svg" />
    <Content Include="app\img\stamp-example.svg" />
    <Content Include="app\components\prototypes\prototypes.js" />
    <Content Include="app\services\certification2022.js" />
    <Content Include="app\services\certification2019.js" />
    <Content Include="app\services\energylabs.js" />
    <Content Include="app\services\adjacentfloorcovering.js" />
    <Content Include="app\services\assessmentsoftwarecompliancemethod.js" />
    <Content Include="app\services\buildingdesigntemplate.js" />
    <Content Include="app\services\buildingconstructiontemplate.js" />
    <Content Include="app\services\buildingservicestemplate.js" />
    <Content Include="app\services\certification.js" />
    <Content Include="app\services\colour.js" />
    <Content Include="app\services\columnvisibility.js" />
    <Content Include="app\services\designchange.js" />
    <Content Include="app\services\glazingcalculatorexport.js" />
    <Content Include="app\services\jobanalytics.js" />
    <Content Include="app\services\materialconstruction.js" />
    <Content Include="app\services\energyplusweatherdata.js" />
    <Content Include="app\services\bushfirestatedata.js" />
    <Content Include="app\services\notificationrule.js" />
    <Content Include="app\services\projecttype.js" />
    <Content Include="app\services\wadesigncode.js" />
    <Content Include="app\services\servicetemplate.js" />
    <Content Include="app\services\nccclimatezonedata.js" />
    <Content Include="app\services\bushfireprone.js" />
    <Content Include="app\services\energyloadlimit.js" />
    <Content Include="app\services\address.js" />
    <Content Include="app\services\adminaudit.js" />
    <Content Include="app\services\assessment.js" />
    <Content Include="app\services\project.js" />
    <Content Include="app\services\standardmodel.js" />
    <Content Include="app\services\wholeofhome.js" />
    <Content Include="app\services\wholeofhomedata.js" />
    <Content Include="app\services\wholeofhomeexport.js" />
    <Content Include="app\services\zone.js" />
    <Content Include="app\services\selectvariablelink.js" />
    <Content Include="app\services\zonesummary.js" />
    <Content Include="app\services\assessmentcomplianceoption.js" />
    <Content Include="app\services\assessmentdrawing.js" />
    <Content Include="app\services\assessmentsoftware.js" />
    <Content Include="app\services\batchjobqueue.js" />
    <Content Include="app\services\buildingexposure.js" />
    <Content Include="app\services\bushfireattacklevel.js" />
    <Content Include="app\services\businessunit.js" />
    <Content Include="app\services\businessunittype.js" />
    <Content Include="app\services\clientdefault.js" />
    <Content Include="app\services\joblistexport.js" />
    <Content Include="app\services\nominatedbuildingsurveyor.js" />
    <Content Include="app\services\worksdescription.js" />
    <Content Include="app\services\slipaddress.js" />
    <Content Include="app\services\metromap.js" />
    <Content Include="app\services\nathersclimatezonepostcode.js" />
    <Content Include="app\services\client.js" />
    <Content Include="app\services\compliancemethod.js" />
    <Content Include="app\services\compliancestatus.js" />
    <Content Include="app\services\construction.js" />
    <Content Include="app\services\country.js" />
    <Content Include="app\services\user.js" />
    <Content Include="app\services\businessrole.js" />
    <Content Include="app\services\eventtype.js" />
    <Content Include="app\services\file.js" />
    <Content Include="app\services\fileversion.js" />
    <Content Include="app\services\frequency.js" />
    <Content Include="app\services\gmap.js" />
    <Content Include="app\services\job.js" />
    <Content Include="app\services\jobevent.js" />
    <Content Include="app\services\jobprojectdetail.js" />
    <Content Include="app\services\loginchecker.js" />
    <Content Include="app\services\lottype.js" />
    <Content Include="app\services\maptouserroles.js" />
    <Content Include="app\services\nathersclimatezone.js" />
    <Content Include="app\services\nccclimatezone.js" />
    <Content Include="app\services\performancerequirementp261.js" />
    <Content Include="app\services\performancerequirementp262.js" />
    <Content Include="app\services\priority.js" />
    <Content Include="app\services\projectdescription.js" />
    <Content Include="app\services\queueservice.js" />
    <Content Include="app\services\integrationaudit.js" />
    <Content Include="app\services\sequentialguid.js" />
    <Content Include="app\services\zonetype.js" />
    <Content Include="app\services\state.js" />
    <Content Include="app\services\status.js" />
    <Content Include="app\services\streettype.js" />
    <Content Include="app\services\mapdata.js" />
    <Content Include="app\services\suburb.js" />
    <Content Include="app\services\manufacturer.js" />
    <Content Include="app\services\template.js" />
    <Content Include="app\services\templatecategory.js" />
    <Content Include="app\services\uploadmonitor.js" />
    <Content Include="app\ui/assessmentdrawing\assessmentdrawing-list-controller.js" />
    <Content Include="app\ui/assessmentdrawing\assessmentdrawing-list.html" />
    <Content Include="app\ui/assessmentdrawing\assessmentdrawing-update-controller.js" />
    <Content Include="app\ui/assessmentdrawing\assessmentdrawing-update.html" />
    <Content Include="app\ui/assessment\assessment-list-controller.js" />
    <Content Include="app\ui/assessment\assessment-list.html" />
    <Content Include="app\ui/assessment\assessment-update-controller.js" />
    <Content Include="app\ui/assessment\assessment-update.html" />
    <Content Include="app\ui/client\client-list-controller.js" />
    <Content Include="app\ui/client\client-list.html" />
    <Content Include="app\ui/client\client-update-controller.js" />
    <Content Include="app\ui/client\client-update.html" />
    <Content Include="app\ui/data/assessmentsoftware\assessmentsoftware-list-controller.js" />
    <Content Include="app\ui/data/assessmentsoftware\assessmentsoftware-list.html" />
    <Content Include="app\ui/data/assessmentsoftware\assessmentsoftware-update-controller.js" />
    <Content Include="app\ui/data/assessmentsoftware\assessmentsoftware-update.html" />
    <Content Include="app\ui/data/buildingexposure\buildingexposure-list-controller.js" />
    <Content Include="app\ui/data/buildingexposure\buildingexposure-list.html" />
    <Content Include="app\ui/data/buildingexposure\buildingexposure-update-controller.js" />
    <Content Include="app\ui/data/buildingexposure\buildingexposure-update.html" />
    <Content Include="app\ui/data/businessunittype\businessunittype-list-controller.js" />
    <Content Include="app\ui/data/businessunittype\businessunittype-list.html" />
    <Content Include="app\ui/data/businessunittype\businessunittype-update-controller.js" />
    <Content Include="app\ui/data/businessunittype\businessunittype-update.html" />
    <Content Include="app\ui/data/businessunit\businessunit-list-controller.js" />
    <Content Include="app\ui/data/businessunit\businessunit-list.html" />
    <Content Include="app\ui/data/businessunit\businessunit-update-controller.js" />
    <Content Include="app\ui/data/businessunit\businessunit-update.html" />
    <Content Include="app\ui/data/compliancemethod\compliancemethod-list-controller.js" />
    <Content Include="app\ui/data/compliancemethod\compliancemethod-list.html" />
    <Content Include="app\ui/data/compliancemethod\compliancemethod-update-controller.js" />
    <Content Include="app\ui/data/compliancemethod\compliancemethod-update.html" />
    <Content Include="app\ui/data/compliancestatus\compliancestatus-list-controller.js" />
    <Content Include="app\ui/data/compliancestatus\compliancestatus-list.html" />
    <Content Include="app\ui/data/compliancestatus\compliancestatus-update-controller.js" />
    <Content Include="app\ui/data/compliancestatus\compliancestatus-update.html" />
    <Content Include="app\ui/data/construction\construction-list-controller.js" />
    <Content Include="app\ui/data/construction\construction-list.html" />
    <Content Include="app\ui/data/construction\construction-update-controller.js" />
    <Content Include="app\ui/data/construction\construction-update.html" />
    <Content Include="app\ui/data/country\country-list-controller.js" />
    <Content Include="app\ui/data/country\country-list.html" />
    <Content Include="app\ui/data/country\country-update-controller.js" />
    <Content Include="app\ui/data/country\country-update.html" />
    <Content Include="app\ui/data/employeerole\employeerole-list-controller.js" />
    <Content Include="app\ui/data/employeerole\employeerole-list.html" />
    <Content Include="app\ui/data/employeerole\employeerole-update-controller.js" />
    <Content Include="app\ui/data/employeerole\employeerole-update.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\assessment-outcomes.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\assessment-outcomes.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\add-option-modal-controller.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\classification-input.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\classification-input.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\add-option-modal.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\loads-override-modal-controller.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\loads-override-modal.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\onselect-popup.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\onselect-popup.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliances\compliance-ele\compliance-ele.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliances\compliance-ele\compliance-ele.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliances\compliance-her\compliance-her.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliances\compliance-her\compliance-her.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliances\compliance-perf\compliance-perf.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliances\compliance-perf\compliance-perf.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\compliance-options.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\compliance-options\compliance-options.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\drawings-upload\drawings-upload.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\drawings-upload\drawings-upload.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\file-upload\file-upload.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\file-upload\file-upload.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\file-upload\generic-file-upload.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\file-upload\generic-file-upload.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\file-upload\software-file-uploads-controller.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\file-upload\software-file-uploads.html" />
    <Content Include="app\ui\assessment\assessment-outcomes\outcomes-summary\outcomes-summary.js" />
    <Content Include="app\ui\assessment\assessment-outcomes\outcomes-summary\outcomes-summary.html" />
    <Content Include="app\ui\assessment\building-column-visibility-modal-controller.js" />
    <Content Include="app\ui\assessment\building-column-visibility-modal.html" />
    <Content Include="app\ui\assessment\building-general-roofs-controller.js" />
    <Content Include="app\ui\assessment\building-general-roofs.html" />
    <Content Include="app\ui\assessment\building-services-data-controller.js" />
    <Content Include="app\ui\assessment\building-services-data.html" />
    <Content Include="app\ui\assessment\climate\climate-overview-controller.js" />
    <Content Include="app\ui\assessment\climate\climate-overview.html" />
    <Content Include="app\ui\assessment\add-element-button.html" />
    <Content Include="app\ui\assessment\add-element-button-controller.js" />
    <Content Include="app\ui\assessment\colour-override-modal\colour-override-modal-controller.js" />
    <Content Include="app\ui\assessment\colour-override-modal\colour-override-modal.html" />
    <Content Include="app\ui\assessment\construction-action-modal\construction-action-modal-controller.js" />
    <Content Include="app\ui\assessment\construction-action-modal\construction-action-modal.html" />
    <Content Include="app\ui\assessment\construction-element-action-modal\construction-element-action-modal-controller.js" />
    <Content Include="app\ui\assessment\construction-element-action-modal\construction-element-action-modal.html" />
    <Content Include="app\ui\assessment\drawings\building-drawings-controller.js" />
    <Content Include="app\ui\assessment\drawings\building-drawings.html" />
    <Content Include="app\ui\assessment\envelope-summary-filters\envelope-summary-filters-controller.js" />
    <Content Include="app\ui\assessment\envelope-summary-filters\envelope-summary-filters.html" />
    <Content Include="app\ui\assessment\floor-plan-data\building-spaces-bulk-edit-controller.js" />
    <Content Include="app\ui\assessment\floor-plan-data\building-spaces-bulk-edit.html" />
    <Content Include="app\ui\assessment\floor-plan-data\floor-plan-areas-controller.js" />
    <Content Include="app\ui\assessment\floor-plan-data\floor-plan-data-controller.js" />
    <Content Include="app\ui\assessment\floor-plan-data\floor-plan-data.html" />
    <Content Include="app\ui\assessment\floor-plan-data\floor-plan-areas.html" />
    <Content Include="app\ui\assessment\glazing-export\export-glazing-calc-modal-controller.js" />
    <Content Include="app\ui\assessment\glazing-export\export-glazing-calc-modal.html" />
    <Content Include="app\ui\assessment\openings-override-modal\openings-override-modal-controller.js" />
    <Content Include="app\ui\assessment\openings-override-modal\openings-override-modal.html" />
    <Content Include="app\ui\assessment\openings-rename-modal\openings-rename-modal-controller.js" />
    <Content Include="app\ui\assessment\openings-rename-modal\openings-rename-modal.html" />
    <Content Include="app\ui\assessment\building-general-design-controller.js" />
    <Content Include="app\ui\assessment\building-general-design.html" />
    <Content Include="app\ui\assessment\results\results-overview-controller.js" />
    <Content Include="app\ui\assessment\results\results-overview.html" />
    <Content Include="app\ui\assessment\reverse-geocode-modal\reverse-geocode-modal-controller.js" />
    <Content Include="app\ui\assessment\reverse-geocode-modal\reverse-geocode-modal.html" />
    <Content Include="app\ui\assessment\search-address-suburb-postcode\search-address-suburb-postcode.html" />
    <Content Include="app\ui\assessment\search-address-suburb-postcode\search-address-suburb-postcode.js" />
    <Content Include="app\ui\assessment\shading-info-modal\shading-info-modal-controller.js" />
    <Content Include="app\ui\assessment\shading-info-modal\shading-info-modal.html" />
    <Content Include="app\ui\assessment\zone-summary.html" />
    <Content Include="app\ui\assessment\zone-summary-controller.js" />
    <Content Include="app\ui\assessment\building-zones-bulk-edit.html" />
    <Content Include="app\ui\assessment\building-zones-bulk-edit-controller.js" />
    <Content Include="app\ui\assessment\building-zone-requirements.html" />
    <Content Include="app\ui\assessment\building-zone-requirements-controller.js" />
    <Content Include="app\ui\assessment\storey-list.html" />
    <Content Include="app\ui\assessment\storey-list-controller.js" />
    <Content Include="app\ui\assessment\zone-list.html" />
    <Content Include="app\ui\assessment\zone-list-controller.js" />
    <Content Include="app\ui\assessment\bulk-edit-modal.html" />
    <Content Include="app\ui\assessment\bulk-edit-modal-controller.js" />
    <Content Include="app\ui\assessment\building-construction-data.html" />
    <Content Include="app\ui\assessment\building-construction-data-controller.js" />
    <Content Include="app\ui\assessment\import-drawings-modal.html" />
    <Content Include="app\ui\assessment\import-drawings-modal-controller.js" />
    <Content Include="app\ui\assessment\group-drawing-update-controller.js" />
    <Content Include="app\ui\assessment\group-drawing-update.html" />
    <Content Include="app\ui\assessment\plannumber-modal-controller.js" />
    <Content Include="app\ui\assessment\plannumber-modal.html" />
    <Content Include="app\ui\assessment\search-street-type\search-street-type.html" />
    <Content Include="app\ui\assessment\search-street-type\search-street-type.js" />
    <Content Include="app\ui\assessment\search-suburb\search-suburb.html" />
    <Content Include="app\ui\assessment\search-suburb\search-suburb.js" />
    <Content Include="app\ui\assessment\search-address\search-address.html" />
    <Content Include="app\ui\assessment\search-address\search-address.js" />
    <Content Include="app\ui\buildingconstructiontemplates\building-construction-template-selector.html" />
    <Content Include="app\ui\buildingconstructiontemplates\building-construction-template-selector-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\building-construction-template-list-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\building-construction-template-list.html" />
    <Content Include="app\ui\buildingconstructiontemplates\building-services-template-selector-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\building-services-template-selector.html" />
    <Content Include="app\ui\buildingconstructiontemplates\building-services-template-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\building-services-template-list-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\building-services-template-list.html" />
    <Content Include="app\ui\buildingconstructiontemplates\building-services-template.html" />
    <Content Include="app\ui\buildingconstructiontemplates\new-building-construction-template-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\new-building-construction-template.html" />
    <Content Include="app\ui\buildingconstructiontemplates\new-building-services-template-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\new-building-services-template.html" />
    <Content Include="app\ui\buildingdesigntemplates\building-design-template-controller.js" />
    <Content Include="app\ui\buildingdesigntemplates\building-design-template.html" />
    <Content Include="app\ui\buildingdesigntemplates\building-design-template-list-controller.js" />
    <Content Include="app\ui\buildingdesigntemplates\building-design-template-list.html" />
    <Content Include="app\ui\buildingdesigntemplates\change-design-template-modal-controller.js" />
    <Content Include="app\ui\buildingdesigntemplates\change-design-template-modal.html" />
    <Content Include="app\ui\buildingdesigntemplates\new-building-design-template-controller.js" />
    <Content Include="app\ui\buildingdesigntemplates\new-building-design-template.html" />
    <Content Include="app\ui\client\client-portal-options-form-controller.js" />
    <Content Include="app\ui\client\client-portal-options-form.html" />
    <Content Include="app\ui\client\client-options-form-controller.js" />
    <Content Include="app\ui\client\client-options-form.html" />
    <Content Include="app\ui\data\bushfirestatedata\bushfirestatedata-list-controller.js" />
    <Content Include="app\ui\data\bushfirestatedata\bushfirestatedata-list.html" />
    <Content Include="app\ui\data\floorheight\floorheight-update-controller.js" />
    <Content Include="app\ui\data\floorheight\floorheight-update.html" />
    <Content Include="app\ui\data\generic-modal-controller.js" />
    <Content Include="app\ui\data\generic-modal.html" />
    <Content Include="app\ui\data\generic-confirmation-modal.html" />
    <Content Include="app\ui\data\generic-confirmation-modal-controller.js" />
    <Content Include="app\ui\data\notificationrule\notification-rule-list-controller.js" />
    <Content Include="app\ui\data\notificationrule\notification-rule-list.html" />
    <Content Include="app\ui\data\notificationrule\notification-rule-update-controller.js" />
    <Content Include="app\ui\data\notificationrule\notification-rule-update.html" />
    <Content Include="app\ui\data\floorcovering\floorcovering-list-controller.js" />
    <Content Include="app\ui\data\floorcovering\floorcovering-list.html" />
    <Content Include="app\ui\data\floorcovering\floorcovering-update-controller.js" />
    <Content Include="app\ui\data\floorcovering\floorcovering-update.html" />
    <Content Include="app\ui\data\construction\construction-import.html" />
    <Content Include="app\ui\data\construction\construction-import-controller.js" />
    <Content Include="app\ui\data\construction\bulk-edit-construction-modal-controller.js" />
    <Content Include="app\ui\data\construction\bulk-edit-construction-modal.html" />
    <Content Include="app\ui\data\designchange\designchange-list-controller.js" />
    <Content Include="app\ui\data\designchange\designchange-list.html" />
    <Content Include="app\ui\data\designchange\designchange-update-controller.js" />
    <Content Include="app\ui\data\designchange\designchange-update.html" />
    <Content Include="app\ui\data\materialconstruction\material-construction-update-controller.js" />
    <Content Include="app\ui\data\materialconstruction\material-construction-update.html" />
    <Content Include="app\ui\data\energyplusweather\epw-update-controller.js" />
    <Content Include="app\ui\data\energyplusweather\epw-update.html" />
    <Content Include="app\ui\data\bushfireprone\bushfireprone-update-controller.js" />
    <Content Include="app\ui\data\bushfireprone\bushfireprone-update.html" />
    <Content Include="app\ui\data\certification\certification-list-controller.js" />
    <Content Include="app\ui\data\certification\certification-list.html" />
    <Content Include="app\ui\data\certification\certification-update-controller.js" />
    <Content Include="app\ui\data\certification\certification-update.html" />
    <Content Include="app\ui\data\colour\colour-list-controller.js" />
    <Content Include="app\ui\data\colour\colour-list.html" />
    <Content Include="app\ui\data\colour\colour-update-controller.js" />
    <Content Include="app\ui\data\colour\colour-update.html" />
    <Content Include="app\ui\data\generic-filter-modal-controller.js" />
    <Content Include="app\ui\data\generic-filter-modal.html" />
    <Content Include="app\ui\data\servicetemplate\servicetemplate-list-controller.js" />
    <Content Include="app\ui\data\servicetemplate\servicetemplate-list.html" />
    <Content Include="app\ui\data\servicetemplate\servicetemplate-update-controller.js" />
    <Content Include="app\ui\data\servicetemplate\bulk-edit-servicetemplate-modal-controller.js" />
    <Content Include="app\ui\data\servicetemplate\bulk-edit-servicetemplate-modal.html" />
    <Content Include="app\ui\data\servicetemplate\servicetemplate-update.html" />
    <Content Include="app\ui\data\nccclimatezonedata\nccclimatezonedata-update-controller.js" />
    <Content Include="app\ui\data\nccclimatezonedata\nccclimatezonedata-update.html" />
    <Content Include="app\ui\data\nominatedbuildingsurveyor\nominatedbuildingsurveyor-list-controller.js" />
    <Content Include="app\ui\data\nominatedbuildingsurveyor\nominatedbuildingsurveyor-list.html" />
    <Content Include="app\ui\data\nominatedbuildingsurveyor\nominatedbuildingsurveyor-update-controller.js" />
    <Content Include="app\ui\data\nominatedbuildingsurveyor\nominatedbuildingsurveyor-update.html" />
    <Content Include="app\ui\data\slipaddressdata\slipaddressdata-update-controller.js" />
    <Content Include="app\ui\data\slipaddressdata\slipaddressdata-update.html" />
    <Content Include="app\ui\data\project\project-update-controller.js" />
    <Content Include="app\ui\data\project\project-update.html" />
    <Content Include="app\ui\data\standard-model\standard-model-update-controller.js" />
    <Content Include="app\ui\data\standard-model\standard-model-update.html" />
    <Content Include="app\ui\data\standard-model-variation\standard-model-variation-update-controller.js" />
    <Content Include="app\ui\data\standard-model-variation\standard-model-variation-update.html" />
    <Content Include="app\ui\data\werslinkfiles\werslinkfiles-controller.js" />
    <Content Include="app\ui\data\werslinkfiles\werslinkfiles.html" />
    <Content Include="app\ui\data\wholeofhomecalculator\wholeofhomecalculator-controller.js" />
    <Content Include="app\ui\data\wholeofhomecalculator\wholeofhomecalculator.html" />
    <Content Include="app\ui\data\wholeofhome\whole-of-home-update-controller.js" />
    <Content Include="app\ui\data\wholeofhome\whole-of-home-update.html" />
    <Content Include="app\ui\data\worksdescription\worksdescription-list.html" />
    <Content Include="app\ui\data\worksdescription\worksdescription-list-controller.js" />
    <Content Include="app\ui\data\worksdescription\worksdescription-update.html" />
    <Content Include="app\ui\data\worksdescription\worksdescription-update-controller.js" />
    <Content Include="app\ui\data\streettype\streettype-list-controller.js" />
    <Content Include="app\ui\data\streettype\streettype-list.html" />
    <Content Include="app\ui\data\streettype\streettype-update-controller.js" />
    <Content Include="app\ui\data\streettype\streettype-update.html" />
    <Content Include="app\ui\data\nathersclimatezonepostcode\nathersclimatezonepostcode-list-controller.js" />
    <Content Include="app\ui\data\nathersclimatezonepostcode\nathersclimatezonepostcode-list.html" />
    <Content Include="app\ui\data\nathersclimatezonepostcode\nathersclimatezonepostcode-update-controller.js" />
    <Content Include="app\ui\data\nathersclimatezonepostcode\nathersclimatezonepostcode-update.html" />
    <Content Include="app\ui\dev\test-area-controller.js" />
    <Content Include="app\ui\dev\test-area.html" />
    <Content Include="app\ui\energy-labs\energy-labs-optimise.html" />
    <Content Include="app\ui\energy-labs\energy-labs-optimise-controller.js" />
    <Content Include="app\ui\energy-labs\home-plan-options-switcher-controller.js" />
    <Content Include="app\ui\energy-labs\home-plan-options-switcher.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-cost-items-modal.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-cost-items-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\zone-summary-variable-select-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\zone-summary-variable-select-modal.html" />
    <Content Include="app\ui\energy-labs\modals\toggle-setting-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\toggle-setting-modal.html" />
    <Content Include="app\ui\energy-labs\modals\variation-scratch-import-modal.html" />
    <Content Include="app\ui\energy-labs\modals\variation-scratch-import-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\orientate-chart-modal.html" />
    <Content Include="app\ui\energy-labs\modals\orientate-chart-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-standard-model-option-modal.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-standard-model-option-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-standard-model-variation-modal.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-standard-model-variation-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\home-model-variation-selector-modal.html" />
    <Content Include="app\ui\energy-labs\modals\home-model-variation-selector-modal.js" />
    <Content Include="app\ui\energy-labs\modals\home-model-selector-modal.html" />
    <Content Include="app\ui\energy-labs\modals\home-model-selector-modal.js" />
    <Content Include="app\ui\energy-labs\modals\copy-design-to-project-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\copy-design-to-project-modal.html" />
    <Content Include="app\ui\energy-labs\modals\home-plan-3d-viewer-modal.html" />
    <Content Include="app\ui\energy-labs\modals\home-plan-3d-viewer-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\home-plan-switcher-modal.html" />
    <Content Include="app\ui\energy-labs\modals\home-plan-switcher-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-project-modal.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-project-modal-controller.js" />
    <Content Include="app\ui\energy-labs\performance-tooltips\home-plan-tooltip.html" />
    <Content Include="app\ui\energy-labs\performance-tooltips\home-plan-tooltip-controller.js" />
    <Content Include="app\ui\energy-labs\sort-list-dropdown-controller.js" />
    <Content Include="app\ui\energy-labs\sort-list-dropdown.html" />
    <Content Include="app\ui\energy-labs\project-list-controller.js" />
    <Content Include="app\ui\energy-labs\project-list.html" />
    <Content Include="app\ui\energy-labs\energy-labs-controller.js" />
    <Content Include="app\ui\energy-labs\energy-labs-configure.html" />
    <Content Include="app\ui\energy-labs\energy-labs-configure-controller.js" />
    <Content Include="app\ui\energy-labs\energy-labs-identify-controller.js" />
    <Content Include="app\ui\energy-labs\energy-labs-identify.html" />
    <Content Include="app\ui\energy-labs\energy-labs-woh-controller.js" />
    <Content Include="app\ui\energy-labs\energy-labs-woh.html" />
    <Content Include="app\ui\energy-labs\energy-labs.html" />
    <Content Include="app\ui\energy-labs\home-plan-switcher-controller.js" />
    <Content Include="app\ui\energy-labs\home-plan-switcher.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-cost-estimate-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-cost-estimate-modal.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-design-insight-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-design-insight-modal.html" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-standard-model-modal-controller.js" />
    <Content Include="app\ui\energy-labs\modals\bulk-edit-standard-model-modal.html" />
    <Content Include="app\ui\energy-labs\performance-tooltips\cooling-tooltip-controller.js" />
    <Content Include="app\ui\energy-labs\performance-tooltips\cooling-tooltip.html" />
    <Content Include="app\ui\energy-labs\performance-tooltips\heating-tooltip-controller.js" />
    <Content Include="app\ui\energy-labs\performance-tooltips\heating-tooltip.html" />
    <Content Include="app\ui\energy-labs\performance-tooltips\rating-tooltip-controller.js" />
    <Content Include="app\ui\energy-labs\performance-tooltips\rating-tooltip.html" />
    <Content Include="app\ui\energy-labs\performance-tooltips\total-tooltip-controller.js" />
    <Content Include="app\ui\energy-labs\performance-tooltips\total-tooltip.html" />
    <Content Include="app\ui\energy-labs\standard-model-assessment-form-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-assessment-form.html" />
    <Content Include="app\ui\energy-labs\standard-model-block-form-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-block-form.html" />
    <Content Include="app\ui\energy-labs\standard-model-cost-estimate-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-cost-estimate.html" />
    <Content Include="app\ui\energy-labs\standard-model-data-icons.html" />
    <Content Include="app\ui\energy-labs\standard-model-data-icons-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-design-insights-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-design-insights.html" />
    <Content Include="app\ui\energy-labs\standard-model-filters-array.html" />
    <Content Include="app\ui\energy-labs\standard-model-filters-array-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-filters-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-filters.html" />
    <Content Include="app\ui\energy-labs\standard-model-variation-selector-list-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-variation-selector-list.html" />
    <Content Include="app\ui\energy-labs\standard-model-variation-options-form.html" />
    <Content Include="app\ui\energy-labs\standard-model-variation-options-form-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-variation-list.html" />
    <Content Include="app\ui\energy-labs\standard-model-variation-list-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-list-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-list.html" />
    <Content Include="app\ui\energy-labs\standard-model-performance-form-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-performance-form.html" />
    <Content Include="app\ui\energy-labs\standard-model-specifications-form-controller.js" />
    <Content Include="app\ui\energy-labs\standard-model-specifications-form.html" />
    <Content Include="app\ui\export\job-list-export-controller.js" />
    <Content Include="app\ui\export\job-list-export.html" />
    <Content Include="app\ui\buildingconstructiontemplates\building-construction-template-controller.js" />
    <Content Include="app\ui\buildingconstructiontemplates\building-construction-template.html" />
    <Content Include="app\ui\home\custom-filter-modal-controller.js" />
    <Content Include="app\ui\home\custom-filter-modal.html" />
    <Content Include="app\ui\home\home-controller.js" />
    <Content Include="app\ui\home\home.html" />
    <Content Include="app\ui\job\analytics-modal.html" />
    <Content Include="app\ui\job\analytics-modal-controller.js" />
    <Content Include="app\ui\job\new-client-contact-modal-controller.js" />
    <Content Include="app\ui\job\new-client-contact-modal.html" />
    <Content Include="app\ui\data\bushfireattacklevel\bushfireattacklevel-list-controller.js" />
    <Content Include="app\ui\data\bushfireattacklevel\bushfireattacklevel-list.html" />
    <Content Include="app\ui\data\bushfireattacklevel\bushfireattacklevel-update-controller.js" />
    <Content Include="app\ui\data\bushfireattacklevel\bushfireattacklevel-update.html" />
    <Content Include="app\ui/data/lottype\lottype-list-controller.js" />
    <Content Include="app\ui/data/lottype\lottype-list.html" />
    <Content Include="app\ui/data/lottype\lottype-update-controller.js" />
    <Content Include="app\ui/data/lottype\lottype-update.html" />
    <Content Include="app\ui/data/nathersclimatezone\nathersclimatezone-list-controller.js" />
    <Content Include="app\ui/data/nathersclimatezone\nathersclimatezone-list.html" />
    <Content Include="app\ui/data/nathersclimatezone\nathersclimatezone-update-controller.js" />
    <Content Include="app\ui/data/nathersclimatezone\nathersclimatezone-update.html" />
    <Content Include="app\ui/data/nccclimatezone\nccclimatezone-list-controller.js" />
    <Content Include="app\ui/data/nccclimatezone\nccclimatezone-list.html" />
    <Content Include="app\ui/data/nccclimatezone\nccclimatezone-update-controller.js" />
    <Content Include="app\ui/data/nccclimatezone\nccclimatezone-update.html" />
    <Content Include="app\ui/data/performancerequirementp261\performancerequirementp261-list-controller.js" />
    <Content Include="app\ui/data/performancerequirementp261\performancerequirementp261-list.html" />
    <Content Include="app\ui/data/performancerequirementp261\performancerequirementp261-update-controller.js" />
    <Content Include="app\ui/data/performancerequirementp261\performancerequirementp261-update.html" />
    <Content Include="app\ui/data/performancerequirementp262\performancerequirementp262-list-controller.js" />
    <Content Include="app\ui/data/performancerequirementp262\performancerequirementp262-list.html" />
    <Content Include="app\ui/data/performancerequirementp262\performancerequirementp262-update-controller.js" />
    <Content Include="app\ui/data/performancerequirementp262\performancerequirementp262-update.html" />
    <Content Include="app\ui\assessmentdrawing\assessmentdrawing-stamp-controller.js" />
    <Content Include="app\ui\assessmentdrawing\assessmentdrawing-stamp.html" />
    <Content Include="app\ui\data\performancerequirementp261\performancerequirementp261code-list-controller.js" />
    <Content Include="app\ui\data\performancerequirementp261\performancerequirementp261code-list.html" />
    <Content Include="app\ui\data\performancerequirementp261\performancerequirementp261code-update-controller.js" />
    <Content Include="app\ui\data\performancerequirementp261\performancerequirementp261code-update.html" />
    <Content Include="app\ui\data\performancerequirementp262\performancerequirementp262code-list-controller.js" />
    <Content Include="app\ui\data\performancerequirementp262\performancerequirementp262code-list.html" />
    <Content Include="app\ui\data\performancerequirementp262\performancerequirementp262code-update-controller.js" />
    <Content Include="app\ui\data\performancerequirementp262\performancerequirementp262code-update.html" />
    <Content Include="app\ui/data/projectdescription\projectdescription-list-controller.js" />
    <Content Include="app\ui/data/projectdescription\projectdescription-list.html" />
    <Content Include="app\ui/data/projectdescription\projectdescription-update-controller.js" />
    <Content Include="app\ui/data/projectdescription\projectdescription-update.html" />
    <Content Include="app\ui\data\zonetype\zonetype-list-controller.js" />
    <Content Include="app\ui\data\zonetype\zonetype-list.html" />
    <Content Include="app\ui\data\zonetype\zonetype-update-controller.js" />
    <Content Include="app\ui\data\zonetype\zonetype-update.html" />
    <Content Include="app\ui/data/state\state-list-controller.js" />
    <Content Include="app\ui/data/state\state-list.html" />
    <Content Include="app\ui/data/state\state-update-controller.js" />
    <Content Include="app\ui/data/state\state-update.html" />
    <Content Include="app\ui/data/status\status-list-controller.js" />
    <Content Include="app\ui/data/status\status-list.html" />
    <Content Include="app\ui/data/status\status-update-controller.js" />
    <Content Include="app\ui/data/status\status-update.html" />
    <Content Include="app\ui/data/suburb\suburb-list-controller.js" />
    <Content Include="app\ui/data/suburb\suburb-list.html" />
    <Content Include="app\ui/data/suburb\suburb-update-controller.js" />
    <Content Include="app\ui/data/suburb\suburb-update.html" />
    <Content Include="app\ui/data/manufacturer\manufacturer-list-controller.js" />
    <Content Include="app\ui/data/manufacturer\manufacturer-list.html" />
    <Content Include="app\ui/data/manufacturer\manufacturer-update-controller.js" />
    <Content Include="app\ui/data/manufacturer\manufacturer-update.html" />
    <Content Include="app\ui/data/templatecategory\templatecategory-list-controller.js" />
    <Content Include="app\ui/data/templatecategory\templatecategory-list.html" />
    <Content Include="app\ui/data/templatecategory\templatecategory-update-controller.js" />
    <Content Include="app\ui/data/templatecategory\templatecategory-update.html" />
    <Content Include="app\ui/data/template\template-list-controller.js" />
    <Content Include="app\ui/data/template\template-list.html" />
    <Content Include="app\ui/data/template\template-update-controller.js" />
    <Content Include="app\ui/data/template\template-update.html" />
    <Content Include="app\ui\login\external-login-verify.html" />
    <Content Include="app\ui\login\external-login-verify-controller.js" />
    <Content Include="app\ui\login\login-controller.js" />
    <Content Include="app\ui\report-files\report-files-controller.js" />
    <Content Include="app\ui\report-files\report-files.html" />
    <Content Include="app\ui\report-settings\report-settings-controller.js" />
    <Content Include="app\ui\report-settings\report-settings.html" />
    <Content Include="app\ui\user\new-user-form.html" />
    <Content Include="app\ui\user\new-user-form-controller.js" />
    <Content Include="app\ui\user\user-list-controller.js" />
    <Content Include="app\ui\user\user-list.html" />
    <Content Include="app\ui\user\new-user-modal.html" />
    <Content Include="app\ui\user\new-user-modal-controller.js" />
    <Content Include="app\ui\user\user-update-controller.js" />
    <Content Include="app\ui\user\user-update.html" />
    <Content Include="app\ui/eventtype\eventtype-list-controller.js" />
    <Content Include="app\ui/eventtype\eventtype-list.html" />
    <Content Include="app\ui/eventtype\eventtype-update-controller.js" />
    <Content Include="app\ui/eventtype\eventtype-update.html" />
    <Content Include="app\ui/fileversion\fileversion-list-controller.js" />
    <Content Include="app\ui/fileversion\fileversion-list.html" />
    <Content Include="app\ui/fileversion\fileversion-update-controller.js" />
    <Content Include="app\ui/fileversion\fileversion-update.html" />
    <Content Include="app\ui/file\file-list-controller.js" />
    <Content Include="app\ui/file\file-list.html" />
    <Content Include="app\ui/file\file-update-controller.js" />
    <Content Include="app\ui/file\file-update.html" />
    <Content Include="app\ui/jobevent\jobevent-list-controller.js" />
    <Content Include="app\ui/jobevent\jobevent-list.html" />
    <Content Include="app\ui/jobevent\jobevent-update-controller.js" />
    <Content Include="app\ui/jobevent\jobevent-update.html" />
    <Content Include="app\ui/jobprojectdetail\jobprojectdetail-list-controller.js" />
    <Content Include="app\ui/jobprojectdetail\jobprojectdetail-list.html" />
    <Content Include="app\ui/jobprojectdetail\jobprojectdetail-update-controller.js" />
    <Content Include="app\ui/jobprojectdetail\jobprojectdetail-update.html" />
    <Content Include="app\ui/job\job-list-controller.js" />
    <Content Include="app\ui/job\job-list.html" />
    <Content Include="app\ui/job\job-update-controller.js" />
    <Content Include="app\ui/job\job-update.html" />
    <Content Include="app\ui/maptouserroles\maptouserroles-list-controller.js" />
    <Content Include="app\ui/maptouserroles\maptouserroles-list.html" />
    <Content Include="app\ui/maptouserroles\maptouserroles-update-controller.js" />
    <Content Include="app\ui/maptouserroles\maptouserroles-update.html" />
    <Content Include="app\ui\admin\adminaudit\adminaudit-list-controller.js" />
    <Content Include="app\ui\admin\adminaudit\adminaudit-list.html" />
    <Content Include="app\ui\admin\adminaudit\adminaudit-update-controller.js" />
    <Content Include="app\ui\admin\adminaudit\adminaudit-update.html" />
    <Content Include="app\ui\data\priority\priority-list-controller.js" />
    <Content Include="app\ui\data\priority\priority-list.html" />
    <Content Include="app\ui\data\priority\priority-update-controller.js" />
    <Content Include="app\ui\data\priority\priority-update.html" />
    <Content Include="app\ui\job\select-files-modal-controller.js" />
    <Content Include="app\ui\job\select-files-modal.html" />
    <Content Include="app\ui\job\print-modal-controller.js" />
    <Content Include="app\ui\job\print-modal.html" />
    <Content Include="app\ui\settings\batchjobqueue\batchjobqueue-list-controller.js" />
    <Content Include="app\ui\settings\batchjobqueue\batchjobqueue-list.html" />
    <Content Include="app\ui\settings\batchjobqueue\batchjobqueue-update-controller.js" />
    <Content Include="app\ui\settings\batchjobqueue\batchjobqueue-update.html" />
    <Content Include="app\ui\settings\frequency\frequency-list-controller.js" />
    <Content Include="app\ui\settings\frequency\frequency-list.html" />
    <Content Include="app\ui\settings\frequency\frequency-update-controller.js" />
    <Content Include="app\ui\settings\frequency\frequency-update.html" />
    <Content Include="app\ui\admin\admin-controller.js" />
    <Content Include="app\ui\admin\admin.html" />
    <Content Include="app\ui\settings\queuerecords\queue-record-list-controller.js" />
    <Content Include="app\ui\settings\queuerecords\queue-record-list.html" />
    <Content Include="app\ui\settings\integrationaudit\integration-audit-list-controller.js" />
    <Content Include="app\ui\settings\integrationaudit\integration-audit-list.html" />
    <Content Include="app\ui\settings\error\error-detail-controller.js" />
    <Content Include="app\ui\settings\error\error-detail.html" />
    <Content Include="app\ui\settings\error\error-list-controller.js" />
    <Content Include="app\ui\settings\error\error-list.html" />
    <Content Include="app\ui\settings\systemparameters\systemparameters-detail-controller.js" />
    <Content Include="app\ui\settings\systemparameters\systemparameters-detail.html" />
    <Content Include="app\ui\settings\systemparameters\systemparameters-list-controller.js" />
    <Content Include="app\ui\settings\systemparameters\systemparameters-list.html" />
    <Content Include="app\ui\admin\useraudit\useraudit-detail-controller.js" />
    <Content Include="app\ui\admin\useraudit\useraudit-detail.html" />
    <Content Include="app\ui\admin\useraudit\useraudit-list-controller.js" />
    <Content Include="app\ui\admin\useraudit\useraudit-list.html" />
    <Content Include="app\ui\admin\users\users-list-controller.js" />
    <Content Include="app\ui\admin\users\users-list.html" />
    <Content Include="app\app.js" />
    <Content Include="app\appconfig-exceptionhandler.js" />
    <Content Include="app\appconfig-messages.js" />
    <Content Include="app\appconfig-route.js" />
    <Content Include="app\appconfig.js" />
    <Content Include="app\components\constants\global-constants.js" />
    <Content Include="app\components\directives\advanced-email-validation-directive.js" />
    <Content Include="app\components\directives\autofocus-directive.js" />
    <Content Include="app\components\directives\back-space-not-back-button-directive.js" />
    <Content Include="app\components\directives\cc-scroll-to-top-directive.js" />
    <Content Include="app\components\directives\cc-sidebar-directive.js" />
    <Content Include="app\components\directives\cc-spinner-directive.js" />
    <Content Include="app\components\directives\cc-widget-action-bar-directive.js" />
    <Content Include="app\components\directives\cc-widget-button-bar-directive.js" />
    <Content Include="app\components\directives\cc-widget-close.js" />
    <Content Include="app\components\directives\cc-widget-content-directive.js" />
    <Content Include="app\components\directives\cc-widget-header-directive.js" />
    <Content Include="app\components\directives\cc-widget-minimize-directive.js" />
    <Content Include="app\components\directives\custom-datepicker-directive.js" />
    <Content Include="app\components\directives\datepicker-popup-directive.js" />
    <Content Include="app\components\directives\directives-directive.js" />
    <Content Include="app\components\directives\dropdown-directives\project-directives.js" />
    <Content Include="app\components\directives\dropdown-keep-open-directive.js" />
    <Content Include="app\components\directives\field-required-directive.js" />
    <Content Include="app\components\directives\focus-me-directive.js" />
    <Content Include="app\components\directives\force-string-directive.js" />
    <Content Include="app\components\directives\http-prefix-directive.js" />
    <Content Include="app\components\directives\my-alert-directive.js" />
    <Content Include="app\components\directives\ng-confirm-click-directive.js" />
    <Content Include="app\components\directives\no-numerics-directive.js" />
    <Content Include="app\components\directives\number-only-directive.js" />
    <Content Include="app\components\directives\only-numeric-directive.js" />
    <Content Include="app\components\directives\password-confirm-directive.js" />
    <Content Include="app\components\directives\phone-format-directive.js" />
    <Content Include="app\components\directives\rc-submit-directive.js" />
    <Content Include="app\components\directives\rd-attachments-directive.js" />
    <Content Include="app\components\directives\rd-back-button-directive.js" />
    <Content Include="app\components\directives\moment-directive.js" />
    <Content Include="app\components\directives\rd-display-created-modified-directive.js" />
    <Content Include="app\components\directives\rd-export-data-directive.js" />
    <Content Include="app\components\directives\rd-match-directive.js" />
    <Content Include="app\components\directives\rd-persist-directive.js" />
    <Content Include="app\components\directives\rd-show-is-modified-directive.js" />
    <Content Include="app\components\directives\rd-show-is-new-directive.js" />
    <Content Include="app\components\directives\set-form-modified-directive.js" />
    <Content Include="app\components\directives\sortable-directive.js" />
    <Content Include="app\components\directives\sys-strength-directive.js" />
    <Content Include="app\components\directives\templates\dropdown-employee-tpl.html" />
    <Content Include="app\components\directives\templates\dropdown-state-tpl.html" />
    <Content Include="app\components\directives\user-name-directive.js" />
    <Content Include="app\components\directives\window-exit-directive.js" />
    <Content Include="app\components\filters\add-leading-zeros.js" />
    <Content Include="app\components\filters\capitalize.js" />
    <Content Include="app\components\filters\local-to-utc.js" />
    <Content Include="app\components\filters\match-on-starting-word.js" />
    <Content Include="app\components\filters\match-on-words.js" />
    <Content Include="app\components\filters\moment-date-fromnow.js" />
    <Content Include="app\components\filters\utc-to-local.js" />
    <Content Include="app\components\security\account\account.js" />
    <Content Include="app\components\security\account\accountform-controller.js" />
    <Content Include="app\components\security\account\form.tpl.html" />
    <Content Include="app\components\security\authorization\authorization-allowroles-directive.js" />
    <Content Include="app\components\security\authorization\authorization.js" />
    <Content Include="app\components\security\authorization\Unauthorised.html" />
    <Content Include="app\components\security\interceptor-service.js" />
    <Content Include="app\components\security\login\form.tpl.html" />
    <Content Include="app\components\security\login\login.js" />
    <Content Include="app\components\security\login\loginform-controller.js" />
    <Content Include="app\components\security\login\toolbar-directive.js" />
    <Content Include="app\components\security\login\toolbar.tpl.html" />
    <Content Include="app\components\security\retryQueue-service.js" />
    <Content Include="app\components\security\security-service.js" />
    <Content Include="app\components\security\security.js" />
    <Content Include="app\components\services\appservices.js" />
    <Content Include="app\components\services\common\bootstrapdialog-service.js" />
    <Content Include="app\components\services\common\common-service.js" />
    <Content Include="app\components\services\common\localizedmessages-service.js" />
    <Content Include="app\components\services\common\log-to-server-service.js" />
    <Content Include="app\components\services\common\logger-service.js" />
    <Content Include="app\components\services\common\spinner-service.js" />
    <Content Include="app\ui\login\login.html" />
    <Content Include="app\ui\menu-parent\menu-parent-controller.js" />
    <Content Include="app\ui\menu-parent\menu-parent.html" />
    <Content Include="app\services\error.js" />
    <Content Include="app\services\loading-bar.js" />
    <Content Include="app\services\systemparameters.js" />
    <Content Include="app\services\versionchecker.js" />
    <Content Include="app\services\useraudit.js" />
    <Content Include="app\services\aspnetusers.js" />
    <Content Include="app\services\usersetting.js" />
    <Content Include="app\ui\layout\navbar-controller.js" />
    <Content Include="app\ui\layout\shell-controller.js" />
    <Content Include="app\ui\layout\shell.html" />
    <Content Include="app\ui\layout\topnavremake.html" />
    <Content Include="app\ui\layout\widgetheader.html" />
    <Content Include="app\ui\settings\systemparameters\systemsettings-controller.js" />
    <Content Include="app\ui\settings\systemparameters\systemsettings.html" />
    <Content Include="app\ui\whole-of-home\whole-of-home-modal-controller.js" />
    <Content Include="app\ui\whole-of-home\whole-of-home-modal.html" />
    <Content Include="App_Readme\Elmah.txt" />
    <Content Include="AspNetIdentify\MigratingMembershipToIdentity.sql" />
    <Content Include="content\angular-material.css" />
    <Content Include="content\angular-material.layout-attributes.css" />
    <Content Include="content\angular-material.layout-attributes.min.css" />
    <Content Include="content\angular-material.layouts.css" />
    <Content Include="content\angular-material.layouts.ie_fixes.css" />
    <Content Include="content\angular-material.layouts.min.css" />
    <Content Include="content\angular-material.min.css" />
    <Content Include="content\angular-tooltips.min.css" />
    <Content Include="content\animate.css" />
    <Content Include="content\Climate Metrics by NatHERS Climate Zone.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\daterangepicker-bs3.css" />
    <Content Include="content\Energy Load Limit Matrix (including NCC 2022 and NSW)_v3.0.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\Energy Load Limit Matrix (including NCC 2022)_v2.0.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\Energy Load Limit Matrix.xlsx" />
    <Content Include="content\energy-labs.css" />
    <Content Include="content\feather\archive.svg" />
    <Content Include="content\feather\copy.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\edit.svg" />
    <Content Include="content\feather\eye-off.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\eye.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\file.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\menu.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\more-horizontal.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\refresh-ccw.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\search.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\trash.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\upload.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\feather\x.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\fileinput.css" />
    <Content Include="content\font-awesome-animation.min.css" />
    <Content Include="content\font-awesome.css" />
    <Content Include="content\font-awesome.min.css" />
    <Content Include="content\font-roboto.css" />
    <Content Include="content\fonts\fontawesome-webfont.svg" />
    <Content Include="content\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="content\fonts\web fonts\roboto_blackitalic_macroman\Roboto-BlackItalic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_blackitalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_blackitalic_macroman\specimen_files\Roboto-BlackItalic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_blackitalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_blackitalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_black_macroman\Roboto-Black-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_black_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_black_macroman\specimen_files\Roboto-Black-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_black_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_black_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_boldcondenseditalic_macroman\RobotoCondensed-BoldItalic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_boldcondenseditalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_boldcondenseditalic_macroman\specimen_files\RobotoCondensed-BoldItalic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_boldcondenseditalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_boldcondenseditalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_boldcondensed_macroman\RobotoCondensed-Bold-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_boldcondensed_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_boldcondensed_macroman\specimen_files\RobotoCondensed-Bold-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_boldcondensed_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_boldcondensed_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_bolditalic_macroman\Roboto-BoldItalic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_bolditalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_bolditalic_macroman\specimen_files\Roboto-BoldItalic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_bolditalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_bolditalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_bold_macroman\Roboto-Bold-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_bold_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_bold_macroman\specimen_files\Roboto-Bold-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_bold_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_bold_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_condenseditalic_macroman\RobotoCondensed-Italic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_condenseditalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_condenseditalic_macroman\specimen_files\RobotoCondensed-Italic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_condenseditalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_condenseditalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_condensed_macroman\RobotoCondensed-Regular-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_condensed_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_condensed_macroman\specimen_files\RobotoCondensed-Regular-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_condensed_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_condensed_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_italic_macroman\Roboto-Italic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_italic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_italic_macroman\specimen_files\Roboto-Italic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_italic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_italic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_lightcondenseditalic_macroman\RobotoCondensed-LightItalic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_lightcondenseditalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_lightcondenseditalic_macroman\specimen_files\RobotoCondensed-LightItalic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_lightcondenseditalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_lightcondenseditalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_lightcondensed_macroman\RobotoCondensed-Light-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_lightcondensed_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_lightcondensed_macroman\specimen_files\RobotoCondensed-Light-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_lightcondensed_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_lightcondensed_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_lightitalic_macroman\Roboto-LightItalic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_lightitalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_lightitalic_macroman\specimen_files\Roboto-LightItalic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_lightitalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_lightitalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_light_macroman\Roboto-Light-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_light_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_light_macroman\specimen_files\Roboto-Light-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_light_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_light_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_mediumitalic_macroman\Roboto-MediumItalic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_mediumitalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_mediumitalic_macroman\specimen_files\Roboto-MediumItalic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_mediumitalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_mediumitalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_medium_macroman\Roboto-Medium-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_medium_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_medium_macroman\specimen_files\Roboto-Medium-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_medium_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_medium_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_regular_macroman\Roboto-Regular-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_regular_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_regular_macroman\specimen_files\Roboto-Regular-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_regular_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_regular_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_thinitalic_macroman\Roboto-ThinItalic-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_thinitalic_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_thinitalic_macroman\specimen_files\Roboto-ThinItalic-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_thinitalic_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_thinitalic_macroman\stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_thin_macroman\Roboto-Thin-demo.html" />
    <Content Include="content\fonts\web fonts\roboto_thin_macroman\specimen_files\grid_12-825-55-15.css" />
    <Content Include="content\fonts\web fonts\roboto_thin_macroman\specimen_files\Roboto-Thin-cleartype.png" />
    <Content Include="content\fonts\web fonts\roboto_thin_macroman\specimen_files\specimen_stylesheet.css" />
    <Content Include="content\fonts\web fonts\roboto_thin_macroman\stylesheet.css" />
    <Content Include="content\ie10mobile.css" />
    <Content Include="content\images\3d-modeling.png" />
    <Content Include="content\images\AngularJS-small.png" />
    <Content Include="content\images\arrow-01.png" />
    <Content Include="content\images\arrow-right.png" />
    <Content Include="content\images\arrow-up-skinny.png" />
    <Content Include="content\images\arrow-up.png" />
    <Content Include="content\images\background.png" />
    <Content Include="content\images\back_disabled.png" />
    <Content Include="content\images\back_enabled.png" />
    <Content Include="content\images\back_enabled_hover.png" />
    <Content Include="content\images\calendar.png" />
    <Content Include="content\images\close.png" />
    <Content Include="content\images\collection.png" />
    <Content Include="content\images\collection_hover.png" />
    <Content Include="content\images\copy.png" />
    <Content Include="content\images\copy_hover.png" />
    <Content Include="content\images\cross.png" />
    <Content Include="content\images\csv.png" />
    <Content Include="content\images\csv_hover.png" />
    <Content Include="content\images\energy-labs\el-area-icon.png" />
    <Content Include="content\images\energy-labs\el-configure-icon.png" />
    <Content Include="content\images\energy-labs\el-assessment-icon.svg" />
    <Content Include="content\images\energy-labs\el-bath-icon [old].svg" />
    <Content Include="content\images\energy-labs\el-bath-icon.png" />
    <Content Include="content\images\energy-labs\el-block-icon.svg" />
    <Content Include="content\images\energy-labs\el-bulb-icon.svg" />
    <Content Include="content\images\energy-labs\el-cost-disabled.svg" />
    <Content Include="content\images\energy-labs\el-cost-enabled.svg" />
    <Content Include="content\images\energy-labs\el-cost-icon.svg" />
    <Content Include="content\images\energy-labs\el-customise-icon [old].svg" />
    <Content Include="content\images\energy-labs\el-default-disabled.svg" />
    <Content Include="content\images\energy-labs\el-default-enabled.svg" />
    <Content Include="content\images\energy-labs\el-design-icon.svg" />
    <Content Include="content\images\energy-labs\el-flame-icon.svg" />
    <Content Include="content\images\energy-labs\el-house-icon [old].svg" />
    <Content Include="content\images\energy-labs\el-identify-icon.svg" />
    <Content Include="content\images\energy-labs\el-launch-arrow-icon.svg" />
    <Content Include="content\images\energy-labs\el-lock-icon.png" />
    <Content Include="content\images\energy-labs\el-north-arrow-icon.svg" />
    <Content Include="content\images\energy-labs\el-optimise-icon.png" />
    <Content Include="content\images\energy-labs\el-orientate-icon.svg" />
    <Content Include="content\images\energy-labs\el-parking-icon [old].svg" />
    <Content Include="content\images\energy-labs\el-parking-icon.png" />
    <Content Include="content\images\energy-labs\el-performance-icon.svg" />
    <Content Include="content\images\energy-labs\el-plug-icon.svg" />
    <Content Include="content\images\energy-labs\el-simple-north-icon.svg" />
    <Content Include="content\images\energy-labs\el-snowflake-icon.svg" />
    <Content Include="content\images\energy-labs\el-sofa-icon.svg" />
    <Content Include="content\images\energy-labs\el-specifications-icon.svg" />
    <Content Include="content\images\energy-labs\el-star-icon.svg" />
    <Content Include="content\images\energy-labs\el-sum-icon.svg" />
    <Content Include="content\images\energy-labs\el-sun-icon.svg" />
    <Content Include="content\images\energy-labs\el-bed-icon.svg" />
    <Content Include="content\images\energy-labs\el-house-icon.png" />
    <Content Include="content\images\energy-labs\el-house-width-icon.png" />
    <Content Include="content\images\eye-hide.png" />
    <Content Include="content\images\eye-open.png" />
    <Content Include="content\images\eye-shut.png" />
    <Content Include="content\images\eye-visible.png" />
    <Content Include="content\images\file.png" />
    <Content Include="content\images\flash.png" />
    <Content Include="content\images\forward_disabled.png" />
    <Content Include="content\images\forward_enabled.png" />
    <Content Include="content\images\forward_enabled_hover.png" />
    <Content Include="content\images\glyphicons-halflings-regular.svg" />
    <Content Include="content\images\gmapblue.png" />
    <Content Include="content\images\gmapgreen.png" />
    <Content Include="content\images\gmapjob1.png" />
    <Content Include="content\images\gmaptechnician1.png" />
    <Content Include="content\images\google-logo.svg" />
    <Content Include="content\images\green-tick.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\images\icon.png" />
    <Content Include="content\images\Jobs.png" />
    <Content Include="content\images\layout.svg" />
    <Content Include="content\images\link-unlink.png" />
    <Content Include="content\images\link.png" />
    <Content Include="content\images\login-logo.png" />
    <Content Include="content\images\logo.png" />
    <Content Include="content\images\Map.png" />
    <Content Include="content\images\microsoft-logo.svg" />
    <Content Include="content\images\north-arrow.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\images\north-arrow.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\images\pop-out.png" />
    <Content Include="content\images\project-design.png" />
    <Content Include="content\images\project-logo-placeholder.png" />
    <Content Include="content\images\project-tool.png" />
    <Content Include="content\images\project-user.png" />
    <Content Include="content\images\red-cross.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\images\Rubber_Stamp-Green.png" />
    <Content Include="content\images\Rubber_Stamp-Grey.png" />
    <Content Include="content\images\select.png" />
    <Content Include="content\images\settings.png" />
    <Content Include="content\images\share.png" />
    <Content Include="content\images\site-bg.png" />
    <Content Include="content\images\pdf.png" />
    <Content Include="content\images\pdf_hover.png" />
    <Content Include="content\images\print.png" />
    <Content Include="content\images\print_hover.png" />
    <Content Include="content\images\query-builder-icon.png" />
    <Content Include="content\images\refresh-icon.png" />
    <Content Include="content\images\reports-icon.png" />
    <Content Include="content\images\separator.png" />
    <Content Include="content\images\sort-arrow.png" />
    <Content Include="content\images\sort-ascending.png" />
    <Content Include="content\images\sort-descending.png" />
    <Content Include="content\images\sort_asc.png" />
    <Content Include="content\images\sort_asc_disabled.png" />
    <Content Include="content\images\sort_both.png" />
    <Content Include="content\images\sort_desc.png" />
    <Content Include="content\images\sort_desc_disabled.png" />
    <Content Include="content\images\Stock.png" />
    <Content Include="content\images\Technicians.png" />
    <Content Include="content\images\thin-download-icon.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\images\tick-white.svg" />
    <Content Include="content\images\up-down-arrow.png" />
    <Content Include="content\images\up_down_arrow.png" />
    <Content Include="content\images\user-cog-solid.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\images\user.png" />
    <Content Include="content\images\white-updown-arrows.png" />
    <Content Include="content\images\xls.png" />
    <Content Include="content\images\xls_hover.png" />
    <Content Include="content\images\zoom-in.png" />
    <Content Include="content\leaflet\bigimage.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\leaflet\geoman.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\leaflet\images\layers-2x.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\leaflet\images\layers.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\leaflet\images\marker-icon-2x.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\leaflet\images\marker-icon.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\leaflet\images\marker-shadow.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\leaflet\leaflet.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\loading-bar.css" />
    <Content Include="content\plus-icon.jpg" />
    <Content Include="content\styles.css" />
    <Content Include="content\thermarate-landing-page.css" />
    <Content Include="content\toastr.css" />
    <Content Include="content\toastr.min.css" />
    <Content Include="content\_scss\delete.css" />
    <Content Include="DatabaseScripts\changelog-base.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="DatabaseScripts\changelog-release1.0.xml" />
    <Content Include="DatabaseScripts\changelog-master.xml" />
    <Content Include="DatabaseScripts\DeleteAllAssessments.sql" />
    <Content Include="DatabaseScripts\HowToSetupLiquibase.txt" />
    <Content Include="ABCpdf.chm" />
    <Content Include="DatabaseScripts\Release1.0\apply-data.sql" />
    <Content Include="DatabaseScripts\Release1.0\alter-tables.sql" />
    <Content Include="DatabaseScripts\Release1.0\add-indexes.sql" />
    <Content Include="DatabaseScripts\Release1.0\create-tables.sql" />
    <Content Include="e_sqlite3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="favicon-16x16.png" />
    <Content Include="favicon-32x32.png" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Gruntfile.js" />
    <Content Include="index.html" />
    <Content Include="lib\AirMovementCalculator.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\EGIS.Controls.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\EGIS.Projections.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\EGIS.ShapeFileLib.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\EPPlus.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\FileHelpers.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\geckodriver.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\geomutil_lib.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\geomutil_libx64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\GlazingCalculatorV1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\GlazingCalculatorV2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\Proj6\x64\proj_6_1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\Proj6\x64\sqlite3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\Proj6\x86\proj_6_1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\Proj6\x86\sqlite3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\ScratchOutputTools.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lib\ScratchTools.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Models\Entities.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>Entities.edmx</DependentUpon>
      <LastGenOutput>Entities.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Models\Entities.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>Entities.edmx</DependentUpon>
      <LastGenOutput>Entities.cs</LastGenOutput>
    </Content>
    <Content Include="NotInUse.txt" />
    <Content Include="parameters.xml" />
    <Content Include="PDFs\Drafts\Needed.txt" />
    <Content Include="PDFs\Needed.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="PrintHook32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="PrintHook64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="safari-pinned-tab.svg" />
    <Content Include="scripts\angular-animate\angular-animate.js" />
    <Content Include="scripts\angular-animate\angular-animate.min.js" />
    <Content Include="scripts\angular-aria\angular-aria.js" />
    <Content Include="scripts\angular-aria\angular-aria.min.js" />
    <Content Include="scripts\angular-cookies.js" />
    <Content Include="scripts\angular-cookies.min.js" />
    <Content Include="scripts\angular-dragdrop.js" />
    <Content Include="scripts\angular-dragdrop.min.js" />
    <Content Include="scripts\angular-filter.js" />
    <Content Include="scripts\angular-material\angular-material-mocks.js" />
    <Content Include="scripts\angular-material\angular-material.js" />
    <Content Include="scripts\angular-material\angular-material.min.js" />
    <Content Include="scripts\angular-messages.js" />
    <Content Include="scripts\angular-messages.min.js" />
    <Content Include="scripts\angular-mocks.js" />
    <Content Include="scripts\angular-moment.js" />
    <Content Include="scripts\angular-sanitize.js" />
    <Content Include="scripts\angular-sanitize.min.js" />
    <Content Include="scripts\angular-tooltips.min.js" />
    <Content Include="scripts\angular-tree-control.js" />
    <Content Include="scripts\angular-treeview.js" />
    <Content Include="scripts\angular-ui-router.js" />
    <Content Include="scripts\angular-ui-router.min.js" />
    <Content Include="scripts\angular-uploadcare.js" />
    <Content Include="scripts\angular-uuid2.js" />
    <Content Include="scripts\angular-uuid2.min.js" />
    <Content Include="scripts\angular-uuid4.js" />
    <Content Include="scripts\angular-uuid4.min.js" />
    <Content Include="package.json" />
    <Content Include="DatabaseScripts\liquibase.properties" />
    <Content Include="content\fonts\Roboto-Bold-webfont.eot" />
    <Content Include="content\fonts\Roboto-Bold-webfont.ttf" />
    <Content Include="content\fonts\Roboto-Light-webfont.eot" />
    <Content Include="content\fonts\Roboto-Light-webfont.ttf" />
    <Content Include="content\fonts\Roboto-Regular-webfont.eot" />
    <Content Include="content\fonts\Roboto-Regular-webfont.ttf" />
    <Content Include="content\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="content\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="content\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="content\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="scripts\angular-aria\angular-aria.min.js.map" />
    <Content Include="scripts\angular-animate\angular-animate.min.js.map" />
    <Content Include="scripts\angular-messages.min.js.map" />
    <EntityDeploy Include="Models\Entities.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>Entities1.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Models\Entities.edmx.diagram">
      <DependentUpon>Entities.edmx</DependentUpon>
    </Content>
    <Content Include="lib\Proj6\proj.db" />
    <Content Include="content\DecimalStarbands.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\scratch\ALL-WINDOWS_2.********.1.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\scratch\ALL-WINDOWS_2.********.1.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\scratch\DEFAULTS_2.********.9.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\scratch\DEFAULTS_2.********.9.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="content\scratch\ALL-WINDOWS_2.********.3.xlsx" />
    <Content Include="content\images\Thumbs.db" />
    <Content Include="content\fonts\new\Roboto-Medium.ttf" />
    <Content Include="content\fonts\new\Roboto-Regular.ttf" />
    <None Include="scripts\angular.intellisense.js" />
    <Content Include="scripts\angular-web-notification.js" />
    <Content Include="scripts\angular\angular.js" />
    <Content Include="scripts\angular\angular.min.js" />
    <Content Include="scripts\ct-ui-router-extras.js" />
    <Content Include="scripts\data-toggle-tooltip.js" />
    <Content Include="scripts\date.js" />
    <Content Include="scripts\daterangepicker.js" />
    <Content Include="scripts\desktop-notify.js" />
    <Content Include="scripts\fileinput.min.js" />
    <Content Include="scripts\heatmap.js" />
    <Content Include="scripts\boost.js" />
    <Content Include="scripts\highcharts-exporting.js" />
    <Content Include="scripts\highcharts-more.js" />
    <Content Include="scripts\highcharts-ng.js" />
    <Content Include="scripts\highcharts.js" />
    <Content Include="scripts\highlight.min.js" />
    <None Include="scripts\jquery-2.0.3.intellisense.js" />
    <Content Include="scripts\jquery-2.0.3.js" />
    <Content Include="scripts\jquery-2.0.3.min.js" />
    <Content Include="scripts\jquery-ui-1.10.3.js" />
    <Content Include="scripts\jquery-ui-1.10.3.min.js" />
    <Content Include="scripts\jSignature\flashcanvas.js" />
    <Content Include="scripts\jSignature\flashcanvas.swf" />
    <Content Include="scripts\jSignature\jSignature.min.js" />
    <Content Include="scripts\jSignature\jSignature.min.noconflict.js" />
    <Content Include="scripts\leaflet\bigimage.js" />
    <Content Include="scripts\leaflet\geoman.js" />
    <Content Include="scripts\leaflet\leaflet.js" />
    <Content Include="scripts\linq.min.js" />
    <Content Include="scripts\lodash.core.js" />
    <Content Include="scripts\lodash.core.min.js" />
    <Content Include="scripts\lodash.js" />
    <Content Include="scripts\lodash.min.js" />
    <Content Include="scripts\lrDragNDrop.js" />
    <Content Include="scripts\lroDragNDrop.js" />
    <Content Include="scripts\lrStickyHeader.js" />
    <Content Include="scripts\markerclusterer.js" />
    <Content Include="scripts\mask.js" />
    <Content Include="scripts\moment.js" />
    <Content Include="scripts\multiselect.js" />
    <Content Include="scripts\ng-bs-daterangepicker.js" />
    <Content Include="scripts\ng-csv.js" />
    <Content Include="scripts\ng-currency.js" />
    <Content Include="scripts\ng-file-upload-shim.js" />
    <Content Include="scripts\ng-file-upload.js" />
    <Content Include="scripts\ng-focus-on.js" />
    <Content Include="scripts\ngmodel.format.js" />
    <Content Include="scripts\ngScrollTo.js" />
    <Content Include="scripts\numeral.min.js" />
    <Content Include="scripts\papaparse.js" />
    <Content Include="scripts\papaparse.min.js" />
    <Content Include="scripts\player\player.css" />
    <Content Include="scripts\player\player.html" />
    <Content Include="scripts\player\player.js" />
    <Content Include="scripts\q.js" />
    <Content Include="scripts\q.min.js" />
    <Content Include="scripts\angular-redactor-2.js" />
    <Content Include="content\redactor.css" />
    <Content Include="scripts\redactor.js" />
    <Content Include="scripts\redactor.min.js" />
    <Content Include="scripts\select.js" />
    <Content Include="scripts\select.min.js" />
    <Content Include="scripts\smart-table.js" />
    <Content Include="scripts\sortable.js" />
    <Content Include="scripts\spin.js" />
    <Content Include="scripts\spin.min.js" />
    <Content Include="scripts\src\angular-uuid4.js" />
    <Content Include="scripts\toastr.js" />
    <Content Include="scripts\toastr.min.js" />
    <Content Include="scripts\ui-utils.js" />
    <Content Include="scripts\ui-utils.min.js" />
    <Content Include="scripts\underscore.js" />
    <Content Include="scripts\XML-2-JSON.js" />
    <Content Include="content\_scss\redactor.scss" />
    <Content Include="content\_scss\_content.scss" />
    <Content Include="content\_scss\_dropdown.scss" />
    <Content Include="content\_scss\_mixins.scss" />
    <Content Include="content\_scss\_modal.scss" />
    <Content Include="content\_scss\_ui.scss" />
    <Content Include="content\_scss\_variables.scss" />
    <None Include="scripts\_references.js" />
    <None Include="SlipDataset\donotdelete">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\AssessmentPDF.html" />
    <Content Include="Templates\Extra\cover_page_footer.svg" />
    <Content Include="Templates\Extra\cover_page_graphics.png" />
    <Content Include="Templates\Extra\<EMAIL>" />
    <Content Include="Templates\Extra\PBDBExplanation-2022PerfEll.png" />
    <Content Include="Templates\Extra\PBDBProcessDiagram-DTS-EP.png" />
    <Content Include="Templates\Extra\PBDBProcessDiagram-VURB_2019.png" />
    <Content Include="Templates\Extra\PBDBProcessDiagram-DTS-HER.png" />
    <Content Include="Templates\Extra\PBDBProcessDiagram-VURB_2022.png" />
    <Content Include="Templates\Extra\PBDBRequirements-2022PerfEll.png" />
    <Content Include="Templates\Extra\Poppins-Regular.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\Extra\Poppins-SemiBold.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\Extra\PreliminaryAssessmentOnlyA3Portrait.svg" />
    <Content Include="Templates\Extra\THERMARATE_logo.jpg" />
    <Content Include="Templates\Extra\THERMARATE_logo.svg" />
    <Content Include="Templates\Extra\WA-HER-Equation-01.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\Extra\WA-HER-Equation-02.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\Extra\WA-HER-Equation-03.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_0 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_1 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_10 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_11 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_12 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_13 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_14 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_15 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_16 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_17 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_18 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_19 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_2 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_20 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_21 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_22 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_23 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_24 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_25 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_26 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_27 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_28 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_29 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_3 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_30 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_31 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_32 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_33 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_34 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_35 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_36 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_37 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_38 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_39 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_4 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_40 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_41 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_42 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_43 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_44 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_45 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_46 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_47 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_48 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_49 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_5 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_50 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_6 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_7 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_8 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2019\NCC 2019_Glazing Calculator_v3.11_9 Rows.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_1 Row.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_10 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_11 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_12 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_13 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_14 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_15 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_16 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_17 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_18 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_19 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_2 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_20 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_21 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_22 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_23 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_24 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_25 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_26 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_27 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_28 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_29 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_3 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_30 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_31 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_32 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_33 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_34 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_35 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_36 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_37 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_38 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_39 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_4 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_40 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_41 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_42 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_43 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_44 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_45 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_46 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_47 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_48 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_49 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_5 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_50 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_51 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_52 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_53 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_54 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_55 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_56 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_57 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_58 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_59 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_6 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_60 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_61 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_62 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_63 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_64 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_65 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_66 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_67 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_68 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_69 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_7 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_70 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_71 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_72 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_73 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_74 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_75 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_76 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_77 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_78 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_79 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_8 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_80 Rows.xlsx" />
    <Content Include="Templates\GlazingCalculator\2022\NCC 2022_Class 1_Glazing Calculator_9 Rows.xlsx" />
    <Content Include="Templates\LightAndVentilationReport.html" />
    <Content Include="Templates\PBDB-NCC2022-PerfELL.html" />
    <Content Include="Templates\PerformanceBasedDesignBrief.html" />
    <Content Include="Templates\SoftwareReport.html" />
    <Content Include="Templates\Specification44Report.html" />
    <Content Include="Templates\SpecificationSummary.html" />
    <Content Include="Templates\Stamps\Stamp_0.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_0.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_1.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_1.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_10.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_2.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_2.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_3.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_3.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_4.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_4.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_5.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_5.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_6.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_6.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_6.6_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_7.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_7.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_8.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_8.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_9.0_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_9.5_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_Complies_ELL.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\Stamps\Stamp_Complies_EP.svg" />
    <Content Include="Templates\Stamps\Stamp_Complies_HER.svg" />
    <Content Include="Templates\Stamps\Stamp_Complies_VURB.svg" />
    <Content Include="Templates\Stamps\Stamp_MultiStar_HER_WithReference.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\Stamps\TEST.svg" />
    <Content Include="Templates\template-styles.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\WohExport\Whole Of Home Calculator_NCC 2022.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Thermarate\Certificate\Information.aspx" />
    <Content Include="Thermarate\Report\diagnostic.aspx" />
    <Content Include="Uploads\DontDelete.txt" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\AutofacConfig.cs" />
    <Compile Include="App_Start\AutoMapperModule.cs" />
    <Compile Include="App_Start\EntityFrameworkStartup.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\QueueProcesses.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.cs" />
    <Compile Include="App_Start\UnitOfWorkModule.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="AspNetIdentify\ApplicationDbContext.cs" />
    <Compile Include="AspNetIdentify\ApplicationRole.cs" />
    <Compile Include="AspNetIdentify\ApplicationRoleManager.cs" />
    <Compile Include="AspNetIdentify\AppUserStore.cs" />
    <Compile Include="AspNetIdentify\Claims.cs" />
    <Compile Include="AspNetIdentify\CustomOAuthProvider.cs" />
    <Compile Include="AspNetIdentify\RoleClaim.cs" />
    <Compile Include="AspNetIdentify\SQLPasswordHasher.cs" />
    <Compile Include="AspNetIdentify\User.cs" />
    <Compile Include="AspNetIdentify\UserClaim.cs" />
    <Compile Include="AspNetIdentify\UserManager.cs" />
    <Compile Include="Background Process\Core\BackgroundProcessor.cs" />
    <Compile Include="Background Process\Processes\CleanupFiles.cs" />
    <Compile Include="Background Process\Processes\CleanupDatabaseTables.cs" />
    <Compile Include="Background Process\Processes\FileDeleter.cs" />
    <Compile Include="Background Process\Processes\MoveFileToS3.cs" />
    <Compile Include="Background Process\Processes\PdfImageCreator.cs" />
    <Compile Include="Background Process\Processes\ProcessRequest.cs" />
    <Compile Include="Background Process\QueueProcesses\GeneratePreliminaryReportQueueProcess.cs" />
    <Compile Include="Background Process\QueueProcesses\GenerateReportQueueProcess.cs" />
    <Compile Include="Background Process\QueueProcesses\NotificationDispatchCheckQueueProcess.cs" />
    <Compile Include="Background Process\QueueProcesses\PDFConversionQueueProcess.cs" />
    <Compile Include="Background Process\QueueProcesses\UpdateGoogleSheets.cs" />
    <Compile Include="Background Process\QueueProcesses\ProcessSlipDataset.cs" />
    <Compile Include="BusHelper\AWSHelpers.cs" />
    <Compile Include="BusHelper\Config.cs" />
    <Compile Include="BusHelper\DateTimeExtensions.cs" />
    <Compile Include="BusHelper\EmailPopulateAndSend.cs" />
    <Compile Include="BusHelper\FileHelper.cs" />
    <Compile Include="BusHelper\GeneratedDocStorageUtil.cs" />
    <Compile Include="BusHelper\Icon.cs" />
    <Compile Include="BusHelper\KeyValueCollection.cs" />
    <Compile Include="BusHelper\SessionObject.cs" />
    <Compile Include="BusHelper\UnhandledExceptionFilter.cs" />
    <Compile Include="BusHelper\UniqueFileName.cs" />
    <Compile Include="BusHelper\UtilityFunctions.cs" />
    <Compile Include="BusHelper\WebApiValidationFilter.cs" />
    <Compile Include="BusinessLogic\AdjacentFloorCovering.cs" />
    <Compile Include="BusinessLogic\AssessmentComplianceBuilding.cs" />
    <Compile Include="BusinessLogic\AssessmentDrawingImport.cs" />
    <Compile Include="BusinessLogic\BushfireStateData.cs" />
    <Compile Include="BusinessLogic\ProjectModelSharedFunctions.cs" />
    <Compile Include="BusinessLogic\ProjectType.cs" />
    <Compile Include="BusinessLogic\PerformanceSolutionEnergyLoadLimit.cs" />
    <Compile Include="BusinessLogic\EnergyLoadLimit.cs" />
    <Compile Include="BusinessLogic\NotificationRule.cs" />
    <Compile Include="BusinessLogic\BuildingDesignTemplate.cs" />
    <Compile Include="BusinessLogic\BuildingServicesTemplate.cs" />
    <Compile Include="BusinessLogic\BuildingSurveyor.cs" />
    <Compile Include="BusinessLogic\BuildingConstructionTemplate.cs" />
    <Compile Include="BusinessLogic\Certification.cs" />
    <Compile Include="BusinessLogic\Colour.cs" />
    <Compile Include="BusinessLogic\DesignChange.cs" />
    <Compile Include="BusinessLogic\EnergyPlusWeatherData.cs" />
    <Compile Include="BusinessLogic\LocalGovernmentArea.cs" />
    <Compile Include="BusinessLogic\LotShape.cs" />
    <Compile Include="BusinessLogic\NotificationFramework.cs" />
    <Compile Include="BusinessLogic\PdfReports\EnergyEfficiencyPdfReportNVelocityData.cs" />
    <Compile Include="BusinessLogic\PdfReports\PdfReportNVelocityData.cs" />
    <Compile Include="BusinessLogic\PdfReports\AssessmentPdfGenerator.cs" />
    <Compile Include="BusinessLogic\PdfReports\SharedPdf.cs" />
    <Compile Include="BusinessLogic\PdfReports\SoftwarePdfReportNVelocityData.cs" />
    <Compile Include="BusinessLogic\PdfReports\SpecificationSummaryPdfReportNVelocityData.cs" />
    <Compile Include="BusinessLogic\PdfReports\StampTools.cs" />
    <Compile Include="BusinessLogic\PdfReports\SvgTools.cs" />
    <Compile Include="BusinessLogic\SuburbBoundariesExtractor.cs" />
    <Compile Include="BusinessLogic\WaDesignCodes.cs" />
    <Compile Include="BusinessLogic\ServiceTemplate.cs" />
    <Compile Include="BusinessLogic\MaterialConstruction.cs" />
    <Compile Include="BusinessLogic\Project.cs" />
    <Compile Include="BusinessLogic\StandardHomeModel.cs" />
    <Compile Include="BusinessLogic\WorksDescription.cs" />
    <Compile Include="BusinessLogic\SlipAddressLocal.cs" />
    <Compile Include="BusinessLogic\SlipAddress.cs" />
    <Compile Include="BusinessLogic\Address.cs" />
    <Compile Include="BusinessLogic\AdminAudit.cs" />
    <Compile Include="BusinessLogic\ArchivePDF.cs" />
    <Compile Include="BusinessLogic\AssessmentRecertification.cs" />
    <Compile Include="BusinessLogic\Assessment.cs" />
    <Compile Include="BusinessLogic\Zone.cs" />
    <Compile Include="BusinessLogic\AssessmentComplianceOption.cs" />
    <Compile Include="BusinessLogic\AssessmentDrawing.cs" />
    <Compile Include="BusinessLogic\AssessmentSoftwareComplianceMethod.cs" />
    <Compile Include="BusinessLogic\AssessmentSoftware.cs" />
    <Compile Include="BusinessLogic\BatchJobQueue.cs" />
    <Compile Include="BusinessLogic\BuildingExposure.cs" />
    <Compile Include="BusinessLogic\BushfireAttackLevel.cs" />
    <Compile Include="BusinessLogic\BusinessLogicBase.cs" />
    <Compile Include="BusinessLogic\BusinessUnit.cs" />
    <Compile Include="BusinessLogic\BusinessUnitType.cs" />
    <Compile Include="BusinessLogic\ClientDefault.cs" />
    <Compile Include="BusinessLogic\EPSimulator.cs" />
    <Compile Include="BusinessLogic\JobListExport.cs" />
    <Compile Include="BusinessLogic\MetroMap.cs" />
    <Compile Include="BusinessLogic\NatHERSClimateZonePostcode.cs" />
    <Compile Include="BusinessLogic\Client.cs" />
    <Compile Include="BusinessLogic\ComplianceMethod.cs" />
    <Compile Include="BusinessLogic\ComplianceStatus.cs" />
    <Compile Include="BusinessLogic\Construction.cs" />
    <Compile Include="BusinessLogic\Country.cs" />
    <Compile Include="BusinessLogic\UserBL.cs" />
    <Compile Include="BusinessLogic\BusinessRole.cs" />
    <Compile Include="BusinessLogic\EventType.cs" />
    <Compile Include="BusinessLogic\File.cs" />
    <Compile Include="BusinessLogic\FileVersion.cs" />
    <Compile Include="BusinessLogic\Frequency.cs" />
    <Compile Include="BusinessLogic\GoogleSheets.cs" />
    <Compile Include="BusinessLogic\IntegrationLog.cs" />
    <Compile Include="BusinessLogic\Job.cs" />
    <Compile Include="BusinessLogic\JobEvent.cs" />
    <Compile Include="BusinessLogic\LotType.cs" />
    <Compile Include="BusinessLogic\MapToUserRoles.cs" />
    <Compile Include="BusinessLogic\NatHERSClimateZone.cs" />
    <Compile Include="BusinessLogic\NCCClimateZone.cs" />
    <Compile Include="BusinessLogic\PerformanceRequirementP261.cs" />
    <Compile Include="BusinessLogic\PerformanceRequirementP262.cs" />
    <Compile Include="BusinessLogic\Priority.cs" />
    <Compile Include="BusinessLogic\ProjectDescription.cs" />
    <Compile Include="BusinessLogic\Queue.cs" />
    <Compile Include="BusinessLogic\ZoneType.cs" />
    <Compile Include="BusinessLogic\State.cs" />
    <Compile Include="BusinessLogic\Status.cs" />
    <Compile Include="BusinessLogic\StreetType.cs" />
    <Compile Include="BusinessLogic\Suburb.cs" />
    <Compile Include="BusinessLogic\Manufacturer.cs" />
    <Compile Include="BusinessLogic\Template.cs" />
    <Compile Include="BusinessLogic\TemplateCategory.cs" />
    <Compile Include="BusinessLogic\UserAudit.cs" />
    <Compile Include="Common\PagingParameters.cs" />
    <Compile Include="Common\StringExtensions.cs" />
    <Compile Include="DtoMapping\AdjacentFloorCoveringProfile.cs" />
    <Compile Include="DtoMapping\BuildingServicesTemplateProfile.cs" />
    <Compile Include="DtoMapping\BushfireStateDataProfile.cs" />
    <Compile Include="DtoMapping\RolesUserSelectionsDataProfile.cs" />
    <Compile Include="DtoMapping\ServiceBatteryTypeProfile.cs" />
    <Compile Include="DtoMapping\ServicePumpTypeProfile.cs" />
    <Compile Include="DtoMapping\StandardHomeModelOptionProfile.cs" />
    <Compile Include="DtoMapping\StandardHomeModelVariationOptionProfile.cs" />
    <Compile Include="DtoMapping\UserProjectProfile.cs" />
    <Compile Include="DtoMapping\ProjectTypeProfile.cs" />
    <Compile Include="DtoMapping\CertificationProfile.cs" />
    <Compile Include="DtoMapping\ColourProfile.cs" />
    <Compile Include="DtoMapping\ConstructionSubCategoryProfile.cs" />
    <Compile Include="DtoMapping\DesignChangeProfile.cs" />
    <Compile Include="DtoMapping\NotificationRuleProfile.cs" />
    <Compile Include="DtoMapping\NotificationRecipientTypeProfile.cs" />
    <Compile Include="DtoMapping\NotificationTemplateProfile.cs" />
    <Compile Include="DtoMapping\SectorDeterminationProfile.cs" />
    <Compile Include="DtoMapping\RulesetProfile.cs" />
    <Compile Include="DtoMapping\ServiceControlDeviceProfile.cs" />
    <Compile Include="DtoMapping\ServiceFuelTypeProfile.cs" />
    <Compile Include="DtoMapping\ServiceTypeProfile.cs" />
    <Compile Include="DtoMapping\ServiceCategoryProfile.cs" />
    <Compile Include="DtoMapping\ICRatingProfile.cs" />
    <Compile Include="DtoMapping\NccOpeningStyleProfile.cs" />
    <Compile Include="DtoMapping\HeatingSystemTypeProfile.cs" />
    <Compile Include="DtoMapping\ServiceTemplateProfile.cs" />
    <Compile Include="DtoMapping\StandardHomeModelFileProfile.cs" />
    <Compile Include="DtoMapping\ProjectProfile.cs" />
    <Compile Include="DtoMapping\StandardHomeModelProfile.cs" />
    <Compile Include="DtoMapping\SurfaceTemplateOpeningTemplateViewProfile.cs" />
    <Compile Include="DtoMapping\AdjacencyProfile.cs" />
    <Compile Include="DtoMapping\AirCavityProfile.cs" />
    <Compile Include="DtoMapping\BuildingDesignTemplateProfile.cs" />
    <Compile Include="DtoMapping\BuildingConstructionTemplateProfile.cs" />
    <Compile Include="DtoMapping\AssessmentComplianceBuildingProfile.cs" />
    <Compile Include="DtoMapping\AssessmentSoftwareComplianceMethodProfile.cs" />
    <Compile Include="DtoMapping\ConstructionCategoryProfile.cs" />
    <Compile Include="DtoMapping\FrameMaterialProfile.cs" />
    <Compile Include="DtoMapping\GlassColourProfile.cs" />
    <Compile Include="DtoMapping\GlassTypeProfile.cs" />
    <Compile Include="DtoMapping\JobListExportProfile.cs" />
    <Compile Include="DtoMapping\AdminAuditProfile.cs" />
    <Compile Include="DtoMapping\OpeningStyleProfile.cs" />
    <Compile Include="DtoMapping\OpeningTemplateProfile.cs" />
    <Compile Include="DtoMapping\UnitOfMeasureProfile.cs" />
    <Compile Include="DtoMapping\WholeOfHomeEfficiencyFactorProfile.cs" />
    <Compile Include="DtoMapping\ZoneProfile.cs" />
    <Compile Include="DtoMapping\AssessmentComplianceOptionProfile.cs" />
    <Compile Include="DtoMapping\AssessmentDrawingProfile.cs" />
    <Compile Include="DtoMapping\AssessmentProfile.cs" />
    <Compile Include="DtoMapping\AssessmentProjectDetailProfile.cs" />
    <Compile Include="DtoMapping\AssessmentSoftwareProfile.cs" />
    <Compile Include="DtoMapping\BatchJobQueueProfile.cs" />
    <Compile Include="DtoMapping\BuildingExposureProfile.cs" />
    <Compile Include="DtoMapping\BushfireAttackLevelProfile.cs" />
    <Compile Include="DtoMapping\BusinessUnitProfile.cs" />
    <Compile Include="DtoMapping\BusinessUnitTypeProfile.cs" />
    <Compile Include="DtoMapping\ClientDefaultProfile.cs" />
    <Compile Include="DtoMapping\NatHERSClimateZonePostcodeProfile.cs" />
    <Compile Include="DtoMapping\BuildingSurveyorProfile.cs" />
    <Compile Include="DtoMapping\WorksDescriptionProfile.cs" />
    <Compile Include="DtoMapping\ProjectRatingModeProfile.cs" />
    <Compile Include="DtoMapping\ClientProfile.cs" />
    <Compile Include="DtoMapping\ComplianceMethodProfile.cs" />
    <Compile Include="DtoMapping\ComplianceStatusProfile.cs" />
    <Compile Include="DtoMapping\SurfaceTemplateProfile.cs" />
    <Compile Include="DtoMapping\CountryProfile.cs" />
    <Compile Include="DtoMapping\UserProfile.cs" />
    <Compile Include="DtoMapping\BusinessRoleProfile.cs" />
    <Compile Include="DtoMapping\ErrorProfile.cs" />
    <Compile Include="DtoMapping\EventTypeProfile.cs" />
    <Compile Include="DtoMapping\FileProfile.cs" />
    <Compile Include="DtoMapping\FileVersionProfile.cs" />
    <Compile Include="DtoMapping\FrequencyProfile.cs" />
    <Compile Include="DtoMapping\IntegrationLogProfile.cs" />
    <Compile Include="DtoMapping\JobEventProfile.cs" />
    <Compile Include="DtoMapping\JobProfile.cs" />
    <Compile Include="DtoMapping\LotTypeProfile.cs" />
    <Compile Include="DtoMapping\RolesBusinessRoleProfile.cs" />
    <Compile Include="DtoMapping\NatHERSClimateZoneProfile.cs" />
    <Compile Include="DtoMapping\NCCClimateZoneProfile.cs" />
    <Compile Include="DtoMapping\PerformanceRequirementP261Profile.cs" />
    <Compile Include="DtoMapping\PerformanceRequirementP262Profile.cs" />
    <Compile Include="DtoMapping\PriorityProfile.cs" />
    <Compile Include="DtoMapping\ProjectDescriptionProfile.cs" />
    <Compile Include="DtoMapping\QueueProfile.cs" />
    <Compile Include="DtoMapping\QueueRecordProfile.cs" />
    <Compile Include="DtoMapping\ZoneActivityProfile.cs" />
    <Compile Include="DtoMapping\NccClassificationProfile.cs" />
    <Compile Include="DtoMapping\ZoneTypeProfile.cs" />
    <Compile Include="DtoMapping\StateProfile.cs" />
    <Compile Include="DtoMapping\StatusProfile.cs" />
    <Compile Include="DtoMapping\StreetTypeProfile.cs" />
    <Compile Include="DtoMapping\SuburbProfile.cs" />
    <Compile Include="DtoMapping\ManufacturerProfile.cs" />
    <Compile Include="DtoMapping\SystemParametersProfile.cs" />
    <Compile Include="DtoMapping\TemplateCategoryProfile.cs" />
    <Compile Include="DtoMapping\TemplateProfile.cs" />
    <Compile Include="DtoMapping\UserAuditProfile.cs" />
    <Compile Include="DtoMapping\AspNetUsersProfile.cs" />
    <Compile Include="Dtos\Range.cs" />
    <Compile Include="Dtos\AssessmentReportSettingsDto.cs" />
    <Compile Include="Dtos\BushfireStateData.cs" />
    <Compile Include="Dtos\FilterDataResultsDto.cs" />
    <Compile Include="Dtos\FilterDataDto.cs" />
    <Compile Include="Dtos\ResetPasswordRequestTokenDto.cs" />
    <Compile Include="Dtos\RolesUserSelectionsDataDto.cs" />
    <Compile Include="Dtos\ServiceBatteryTypeDto.cs" />
    <Compile Include="Dtos\ServicePumpTypeDto.cs" />
    <Compile Include="Dtos\StandardHomeModelOptionDto.cs" />
    <Compile Include="Dtos\StandardHomeModelVariationOptionDto.cs" />
    <Compile Include="Dtos\TokenDto.cs" />
    <Compile Include="Dtos\UserProjectDto.cs" />
    <Compile Include="Dtos\ClimateChartDataDto.cs" />
    <Compile Include="Dtos\AddressDto.cs" />
    <Compile Include="Dtos\AdjacencyDto.cs" />
    <Compile Include="Dtos\AdjacentFloorCoveringDto.cs" />
    <Compile Include="Dtos\AdminAuditDto.cs" />
    <Compile Include="Dtos\AirCavityDto.cs" />
    <Compile Include="Dtos\BuildingServicesTemplateDto.cs" />
    <Compile Include="Dtos\CertificationDto.cs" />
    <Compile Include="Dtos\ColourDto.cs" />
    <Compile Include="Dtos\ConstructionSubCategoryDto.cs" />
    <Compile Include="Dtos\ConstructionCategoryDto.cs" />
    <Compile Include="Dtos\ConstructionFilterDataDto.cs" />
    <Compile Include="Dtos\DesignChangeDto.cs" />
    <Compile Include="Dtos\EnergyResultsChartDataDto.cs" />
    <Compile Include="Dtos\FrameMaterialDto.cs" />
    <Compile Include="Dtos\GlassColourDto.cs" />
    <Compile Include="Dtos\GlassTypeDto.cs" />
    <Compile Include="Dtos\NccOpeningStyleDto.cs" />
    <Compile Include="Dtos\NotificationRecipientTypeDto.cs" />
    <Compile Include="Dtos\NotificationRuleDto.cs" />
    <Compile Include="Dtos\NotificationTemplateDto.cs" />
    <Compile Include="Dtos\ProjectTypeDto.cs" />
    <Compile Include="Dtos\SectorDeterminationDto.cs" />
    <Compile Include="Dtos\RulesetDto.cs" />
    <Compile Include="Dtos\ServiceFuelTypeDto.cs" />
    <Compile Include="Dtos\ServiceTemplateDto.cs" />
    <Compile Include="Dtos\OpeningStyleDto.cs" />
    <Compile Include="Dtos\ProjectDto.cs" />
    <Compile Include="Dtos\StandardHomeModelDto.cs" />
    <Compile Include="Dtos\StandardHomeModelFileDto.cs" />
    <Compile Include="Dtos\SurfaceOpeningViewDto.cs" />
    <Compile Include="Dtos\UnitOfMeasureDto.cs" />
    <Compile Include="Dtos\NccClassificationDto.cs" />
    <Compile Include="Dtos\WholeOfHomeEfficiencyFactorDto.cs" />
    <Compile Include="Dtos\EnvelopeSummaryDto.cs" />
    <Compile Include="Dtos\ZoneSummaryDto.cs" />
    <Compile Include="Dtos\ZoneDto.cs" />
    <Compile Include="Dtos\AssessmentComplianceBuildingDto.cs" />
    <Compile Include="Dtos\AssessmentComplianceOptionDto.cs" />
    <Compile Include="Dtos\AssessmentDrawingConvertDto.cs" />
    <Compile Include="Dtos\BuildingDesignTemplateDto.cs" />
    <Compile Include="Dtos\BuildingConstructionTemplateDto.cs" />
    <Compile Include="Dtos\BuildingSurveyorDto.cs" />
    <Compile Include="Dtos\AssessmentDrawingDto.cs" />
    <Compile Include="Dtos\AssessmentDto.cs" />
    <Compile Include="Dtos\AssessmentNavigationDto.cs" />
    <Compile Include="Dtos\AssessmentProjectDetailDto.cs" />
    <Compile Include="Dtos\AssessmentSoftwareComplianceMethodDto.cs" />
    <Compile Include="Dtos\AssessmentSoftwareDto.cs" />
    <Compile Include="Dtos\BatchJobQueueDto.cs" />
    <Compile Include="Dtos\BuildingExposureDto.cs" />
    <Compile Include="Dtos\BushfireAttackLevelDto.cs" />
    <Compile Include="Dtos\BusinessUnitDto.cs" />
    <Compile Include="Dtos\BusinessUnitTypeDto.cs" />
    <Compile Include="Dtos\ClientDefaultDto.cs" />
    <Compile Include="Dtos\AssessmentDrawingProcessingParamsDto.cs" />
    <Compile Include="Dtos\CopyAssessmentDto.cs" />
    <Compile Include="Dtos\DtoBase.cs" />
    <Compile Include="Dtos\EnergyLoadLimitDto.cs" />
    <Compile Include="Dtos\EPSimulatorDto.cs" />
    <Compile Include="Dtos\JobListExportDto.cs" />
    <Compile Include="Dtos\MapImageDto.cs" />
    <Compile Include="Dtos\NatHERSClimateZoneDtoPostcode.cs" />
    <Compile Include="Dtos\WorksDescriptionDto.cs" />
    <Compile Include="Dtos\ProjectRatingModeDto.cs" />
    <Compile Include="Dtos\ClientDto.cs" />
    <Compile Include="Dtos\ComplianceMethodDto.cs" />
    <Compile Include="Dtos\ComplianceStatusCodeDto.cs" />
    <Compile Include="Dtos\ComplianceStatusDto.cs" />
    <Compile Include="Dtos\ConstructionTemplateDto.cs" />
    <Compile Include="Dtos\CountryDto.cs" />
    <Compile Include="Dtos\UserDto.cs" />
    <Compile Include="Dtos\BusinessRoleDto.cs" />
    <Compile Include="Dtos\EventTypeDto.cs" />
    <Compile Include="Dtos\ExternalFinishColourDto.cs" />
    <Compile Include="Dtos\FileDto.cs" />
    <Compile Include="Dtos\FileVersionDto.cs" />
    <Compile Include="Dtos\FrequencyDto.cs" />
    <Compile Include="Dtos\GeocodeDto.cs" />
    <Compile Include="Dtos\ErrorDto.cs" />
    <Compile Include="Dtos\ImportDrawingsDto.cs" />
    <Compile Include="Dtos\IntegrationLogDto.cs" />
    <Compile Include="Dtos\JobDto.cs" />
    <Compile Include="Dtos\JobEventDto.cs" />
    <Compile Include="Dtos\LotTypeDto.cs" />
    <Compile Include="Dtos\MapRolesBusinessRoleDto.cs" />
    <Compile Include="Dtos\NatHERSClimateZoneDto.cs" />
    <Compile Include="Dtos\NCCClimateZoneDto.cs" />
    <Compile Include="Dtos\PerformanceRequirementP261Dto.cs" />
    <Compile Include="Dtos\PerformanceRequirementP262Dto.cs" />
    <Compile Include="Dtos\PriorityDto.cs" />
    <Compile Include="Dtos\ProjectDescriptionDto.cs" />
    <Compile Include="Dtos\QueueDto.cs" />
    <Compile Include="Dtos\QueueRecordDto.cs" />
    <Compile Include="Dtos\ZoneActivityDto.cs" />
    <Compile Include="Dtos\ZoneTypeDto.cs" />
    <Compile Include="Dtos\StateDto.cs" />
    <Compile Include="Dtos\StatusDto.cs" />
    <Compile Include="Dtos\StreetTypeDto.cs" />
    <Compile Include="Dtos\SuburbDto.cs" />
    <Compile Include="Dtos\ManufacturerDto.cs" />
    <Compile Include="Dtos\SystemInfoDto.cs" />
    <Compile Include="Dtos\SystemParametersDto.cs" />
    <Compile Include="Dtos\TemplateCategoryDto.cs" />
    <Compile Include="Dtos\TemplateDto.cs" />
    <Compile Include="Dtos\UserAuditDto.cs" />
    <Compile Include="Dtos\AspNetUsersDto.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="GlobalVariables.cs" />
    <Compile Include="Helpers\EFExtensions.cs" />
    <Compile Include="Helpers\Extensions.cs" />
    <Compile Include="Helpers\ServiceLocator.cs" />
    <Compile Include="Helpers\UnitOfWork.cs" />
    <Compile Include="Models\AspNetRole.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\AspNetUser.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\AspNetUserClaim.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\AspNetUserLogin.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\aspnet_Applications.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\C__ViewCache.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\EdmxClassExtensions.cs" />
    <Compile Include="Models\ELMAH_Error.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Entities.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Entities.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Entities.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Entities.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Entities.edmx</DependentUpon>
    </Compile>
    <Compile Include="Models\Entities1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Entities.edmx</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Adjacency.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AdjacentFloorCovering.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AdminAudit.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AirCavity.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Assessment.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AssessmentComplianceBuilding.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AssessmentComplianceOption.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AssessmentDrawing.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AssessmentProjectDetail.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AssessmentSoftware.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_AssessmentSoftwareComplianceMethod.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BatchJobQueue.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BuildingConstructionTemplate.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BuildingDesignTemplate.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BuildingExposure.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BuildingServicesTemplate.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BuildingSurveyor.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BushfireAttackLevel.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BushFireProneData.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BushfireStateData.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BusinessRole.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BusinessUnit.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_BusinessUnitType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Certification.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Client.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ClientDefault.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Colour.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ComplianceMethod.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ComplianceStatus.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ConstructionCategory.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ConstructionSubCategory.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Country.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_DesignChange.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_EnergyPlusWeatherData.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_EnergyPlusWeatherHeaderData.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_EPSimulatorReport.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_EventType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_File.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_FileVersion.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_FrameMaterial.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Frequency.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_GlassColour.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_GlassType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_HeatingSystemType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ICRating.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_IntegrationLog.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Job.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_JobAnalyticsDetail_View.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_JobAssessmentProjectDetail_View.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_JobEvent.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_LocalGovernmentArea.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_LotType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Manufacturer.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_MaterialConstruction.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NatHERSClimateZone.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NatHERSClimateZonePostcode.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NCCClassification.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NCCClimateZone.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NccClimateZoneData.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NccOpeningStyle.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NextNumber.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NotificationDispatchCheckRequest.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NotificationRecipientType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NotificationRule.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_NotificationTemplate.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_OpeningStyle.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_OpeningTemplate.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_OrderType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_PerformanceRequirementP261.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_PerformanceRequirementP262.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Priority.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Project.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ProjectDescription.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ProjectRatingMode.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ProjectType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Queue.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_QueueRecord.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_RolesBusinessRole.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_RolesUserSelectionsData.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Ruleset.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_SectorDetermination.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServiceBatteryType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServiceCategory.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServiceControlDevice.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServiceFuelType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServicePumpType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServiceTemplate.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServiceTemplateView.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ServiceType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_StandardHomeModel.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_StandardHomeModelFile.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_StandardHomeModelOption.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_StandardHomeModelVariationOption.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_State.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Status.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_StreetType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Suburb.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_SurfaceTemplate.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_SurfaceTemplateOpeningTemplateView.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_SystemParameters.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Template.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_TemplateCategory.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_UnitOfMeasure.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_User.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_UserAudit.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_UserProject.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_UsersClient.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_WaDesignCode.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_WholeOfHomeEfficiencyFactor.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_WorksDescription.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_Zone.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ZoneActivity.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RSS_ZoneType.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\vw_AspNetUserRoles.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\vw_aspnet_Applications.cs">
      <DependentUpon>Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Redi Email Send\EmailLog.cs" />
    <Compile Include="Redi Email Send\EmailLogProvider.cs" />
    <Compile Include="Redi Email Send\Emails.cs" />
    <Compile Include="Redi Email Send\SendEmailMsg.cs" />
    <Compile Include="Redi Utility\Base32.cs" />
    <Compile Include="Redi Utility\BusErrorList.cs" />
    <Compile Include="Redi Utility\CacheHandler.cs" />
    <Compile Include="Redi Utility\CalcGST.cs" />
    <Compile Include="Redi Utility\DataAccess.cs" />
    <Compile Include="Redi Utility\Encryptor.cs" />
    <Compile Include="Redi Utility\ErrorMessage.cs" />
    <Compile Include="Redi Utility\ErrorWriter.cs" />
    <Compile Include="Redi Utility\Frequency.cs" />
    <Compile Include="Redi Utility\GlobalThreads.cs" />
    <Compile Include="Redi Utility\MapFileContentType.cs" />
    <Compile Include="Redi Utility\MergePdfs.cs" />
    <Compile Include="Redi Utility\Mod10.cs" />
    <Compile Include="Redi Utility\NextNumber.cs" />
    <Compile Include="Redi Utility\Pdf Reports\AssessmentCompliancePdfReport.cs" />
    <Compile Include="Redi Utility\Pdf Reports\BasePdfReport.cs" />
    <Compile Include="Redi Utility\Pdf Reports\LightAndVentilationPdfReport.cs" />
    <Compile Include="Redi Utility\Pdf Reports\PerformanceBasedDesignBriefPdfReport.cs" />
    <Compile Include="Redi Utility\Pdf Reports\AssessmentSoftwarePdfReport.cs" />
    <Compile Include="Redi Utility\Pdf Reports\Specification44PdfReport.cs" />
    <Compile Include="Redi Utility\Pdf Reports\SpecificationSummaryPdfReport.cs" />
    <Compile Include="Redi Utility\PropertyCopier.cs" />
    <Compile Include="Redi Utility\ResizeImage.cs" />
    <Compile Include="Redi Utility\SequentialGuid.cs" />
    <Compile Include="Redi Utility\Serialise.cs" />
    <Compile Include="Redi Utility\SQLRetry.cs" />
    <Compile Include="Redi Utility\StreetAddress.cs" />
    <Compile Include="Redi Utility\SystemParameter.cs" />
    <Compile Include="Redi Utility\TemplateDoc.cs" />
    <Compile Include="Redi Utility\TimeHelper.cs" />
    <Compile Include="Security\AuthenticatorDetailsDto.cs" />
    <Compile Include="Security\CurrentUser.cs" />
    <Compile Include="Security\ChallengeResult.cs" />
    <Compile Include="Security\Security.cs" />
    <Compile Include="Security\SecurityConfig.cs" />
    <Compile Include="Services\BushfireStateDataController.cs" />
    <Compile Include="Services\ExternalLoginController.cs" />
    <Compile Include="Services\JobAnalyticsController.cs" />
    <Compile Include="Services\WholeOfHomeExportController.cs" />
    <Compile Include="Services\_TestController.cs" />
    <Compile Include="Services\AdjacentFloorCoveringController.cs" />
    <Compile Include="Services\ProjectTypeController.cs" />
    <Compile Include="Services\NotificationRuleController.cs" />
    <Compile Include="Services\BuildingDesignTemplateController.cs" />
    <Compile Include="Services\BuildingServicesTemplateController.cs" />
    <Compile Include="Services\CertificationController.cs" />
    <Compile Include="Services\ColourController.cs" />
    <Compile Include="Services\DesignChangeController.cs" />
    <Compile Include="Services\GlazingCalculatorExportController.cs" />
    <Compile Include="Services\MaterialConstructionController.cs" />
    <Compile Include="Services\EnergyPlusWeatherDataController.cs" />
    <Compile Include="Services\WaDesignCodesController.cs" />
    <Compile Include="Services\ServiceTemplateController.cs" />
    <Compile Include="Services\NccClimateZoneDataController.cs" />
    <Compile Include="Services\BushFireProneController.cs" />
    <Compile Include="Services\BuildingSurveyorController.cs" />
    <Compile Include="Services\BuildingConstructionTemplateController.cs" />
    <Compile Include="Services\ProjectController.cs" />
    <Compile Include="Services\StandardModelController.cs" />
    <Compile Include="Services\WholeOfHomeDataController.cs" />
    <Compile Include="Services\WorksDescriptionController.cs" />
    <Compile Include="Services\SlipController.cs" />
    <Compile Include="Services\AddressController.cs" />
    <Compile Include="Services\AdminAuditController.cs" />
    <Compile Include="Services\ZoneController.cs" />
    <Compile Include="Services\AssessmentComplianceOptionController.cs" />
    <Compile Include="Services\AssessmentController.cs" />
    <Compile Include="Services\AssessmentDrawingController.cs" />
    <Compile Include="Services\AssessmentSoftwareComplianceMethodController.cs" />
    <Compile Include="Services\AssessmentSoftwareController.cs" />
    <Compile Include="Services\BatchJobQueueController.cs" />
    <Compile Include="Services\BuildingExposureController.cs" />
    <Compile Include="Services\BushfireAttackLevelController.cs" />
    <Compile Include="Services\BusinessUnitController.cs" />
    <Compile Include="Services\BusinessUnitTypeController.cs" />
    <Compile Include="Services\ClientDefaultController.cs" />
    <Compile Include="Services\EnergyLoadLimitController.cs" />
    <Compile Include="Services\EPSimulatorController.cs" />
    <Compile Include="Services\JobListExportController.cs" />
    <Compile Include="Services\MetroMapController.cs" />
    <Compile Include="Services\NatHERSClimateZonePostcodeController.cs" />
    <Compile Include="Services\ClientController.cs" />
    <Compile Include="Services\ComplianceMethodController.cs" />
    <Compile Include="Services\ComplianceStatusController.cs" />
    <Compile Include="Services\ConstructionController.cs" />
    <Compile Include="Services\CountryController.cs" />
    <Compile Include="Services\UserController.cs" />
    <Compile Include="Services\BusinessRoleController.cs" />
    <Compile Include="Services\ErrorController.cs" />
    <Compile Include="Services\EventTypeController.cs" />
    <Compile Include="Services\FileController.cs" />
    <Compile Include="Services\FileVersionController.cs" />
    <Compile Include="Services\FrequencyController.cs" />
    <Compile Include="Services\IntegrationAuditController.cs" />
    <Compile Include="Services\JobController.cs" />
    <Compile Include="Services\JobEventController.cs" />
    <Compile Include="Services\LoginCheckerController.cs" />
    <Compile Include="Services\MapDataProxyController.cs" />
    <Compile Include="Services\LotTypeController.cs" />
    <Compile Include="Services\MapToUserRolesController.cs" />
    <Compile Include="Services\NatHERSClimateZoneController.cs" />
    <Compile Include="Services\NCCClimateZoneController.cs" />
    <Compile Include="Services\PerformanceRequirementP261Controller.cs" />
    <Compile Include="Services\PerformanceRequirementP262Controller.cs" />
    <Compile Include="Services\PriorityController.cs" />
    <Compile Include="Services\ProjectDescriptionController.cs" />
    <Compile Include="Services\QueueController.cs" />
    <Compile Include="Services\SecurityController.cs" />
    <Compile Include="Services\SequentialGuidController.cs" />
    <Compile Include="Services\ZoneTypeController.cs" />
    <Compile Include="Services\StateController.cs" />
    <Compile Include="Services\StatusController.cs" />
    <Compile Include="Services\StreetTypeController.cs" />
    <Compile Include="Services\SuburbController.cs" />
    <Compile Include="Services\ManufacturerController.cs" />
    <Compile Include="Services\SystemConfigController.cs" />
    <Compile Include="Services\SystemParametersController.cs" />
    <Compile Include="Services\TemplateCategoryController.cs" />
    <Compile Include="Services\TemplateController.cs" />
    <Compile Include="Services\UploadGlazingSpreedsheetController.cs" />
    <Compile Include="Services\UploadSuburbSpreedsheetController.cs" />
    <Compile Include="Services\UserAuditController.cs" />
    <Compile Include="Services\AspNetUsersController.cs" />
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="Thermarate\Certificate\Information.aspx.cs">
      <DependentUpon>Information.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Thermarate\Certificate\Information.aspx.designer.cs">
      <DependentUpon>Information.aspx</DependentUpon>
    </Compile>
    <Compile Include="Thermarate\Report\diagnostic.aspx.cs">
      <DependentUpon>diagnostic.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Thermarate\Report\diagnostic.aspx.designer.cs">
      <DependentUpon>diagnostic.aspx</DependentUpon>
    </Compile>
    <Compile Include="Utils\Cypher.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="content\fonts\fontawesome-webfont.eot" />
    <Content Include="content\fonts\fontawesome-webfont.ttf" />
    <Content Include="content\fonts\fontawesome-webfont.woff" />
    <Content Include="content\fonts\FontAwesome.otf" />
    <Content Include="content\images\glyphicons-halflings-regular.eot" />
    <Content Include="content\images\glyphicons-halflings-regular.ttf" />
    <Content Include="content\images\glyphicons-halflings-regular.woff" />
    <Content Include="content\images\Sorting icons.psd" />
    <Content Include="content\toastr.less" />
    <Content Include="scripts\angular-cookies.min.js.map" />
    <Content Include="scripts\angular-sanitize.min.js.map" />
    <Content Include="scripts\jquery-2.0.3.min.map" />
    <Content Include="scripts\select.min.js.map" />
    <Content Include="scripts\toastr.min.js.map" />
    <Content Include="Thermarate-Assessment.wpp.targets" />
    <Content Include="Templates\Extra\THERMARATE_logo.eps" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="app\components\menu\" />
    <Folder Include="app\ui\data\supplier\" />
    <Folder Include="app\ui\settings\dataconfig\" />
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DynamicLinq\Kendo.DynamicLinq.csproj">
      <Project>{2bd75d53-e0ea-4995-8b0f-60ad709945ac}</Project>
      <Name>Kendo.DynamicLinq</Name>
    </ProjectReference>
    <ProjectReference Include="..\TenureExtraction\TenureExtraction.csproj">
      <Project>{0705c014-bc27-4ad3-95fa-d39202d0aec8}</Project>
      <Name>TenureExtraction</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup />
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>50520</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:50520/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
    <MonoDevelop>
      <Properties>
        <XspParameters Port="8080" Address="127.0.0.1" SslMode="None" SslProtocol="Default" KeyType="None" CertFile="" KeyFile="" PasswordOptions="None" Password="" Verbose="True" />
      </Properties>
    </MonoDevelop>
  </ProjectExtensions>
  <Import Project="..\packages\SQLitePCLRaw.lib.e_sqlite3.2.0.6\build\net461\SQLitePCLRaw.lib.e_sqlite3.targets" Condition="Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.2.0.6\build\net461\SQLitePCLRaw.lib.e_sqlite3.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\SQLitePCLRaw.lib.e_sqlite3.2.0.6\build\net461\SQLitePCLRaw.lib.e_sqlite3.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SQLitePCLRaw.lib.e_sqlite3.2.0.6\build\net461\SQLitePCLRaw.lib.e_sqlite3.targets'))" />
    <Error Condition="!Exists('..\packages\Scriban.5.7.0\build\Scriban.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Scriban.5.7.0\build\Scriban.props'))" />
    <Error Condition="!Exists('..\packages\Scriban.5.7.0\build\Scriban.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Scriban.5.7.0\build\Scriban.targets'))" />
    <Error Condition="!Exists('..\packages\SkiaSharp.1.68.0\build\net45\SkiaSharp.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SkiaSharp.1.68.0\build\net45\SkiaSharp.targets'))" />
  </Target>
  <Import Project="..\packages\Scriban.5.7.0\build\Scriban.targets" Condition="Exists('..\packages\Scriban.5.7.0\build\Scriban.targets')" />
  <Import Project="..\packages\SkiaSharp.1.68.0\build\net45\SkiaSharp.targets" Condition="Exists('..\packages\SkiaSharp.1.68.0\build\net45\SkiaSharp.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>